from fastapi import APIRouter, Depends, HTTPException, Query, Body
from typing import Dict, Any, Optional, List
import json

from app.core.security import get_current_user
from app.core.logger import get_logger

# 导入云函数
from app.cloud_functions.weekly_report_functions.index import main_handler as weekly_report_functions_handler

logger = get_logger(__name__)
router = APIRouter()

@router.get("/weekly")
async def get_weekly_reports(
    project_code: Optional[str] = Query(None, description="项目编号"),
    week: Optional[str] = Query(None, description="周次(YYYY-WW)"),
    current_user: Dict = Depends(get_current_user)
) -> Dict[str, Any]:
    """
    获取周报
    """
    try:
        # 构造云函数需要的event对象
        event = {
            "queryStringParameters": {
                "action": "get",
                "project_code": project_code,
                "week": week
            }
        }
        
        # 调用云函数
        result = weekly_report_functions_handler(event, None)
        
        # 解析云函数响应
        if result.get("statusCode") == 200:
            body = json.loads(result.get("body", "{}"))
            return body
        else:
            logger.error(f"获取周报失败: {result}")
            raise HTTPException(status_code=result.get("statusCode", 500), detail="获取周报失败")
    except Exception as e:
        logger.error(f"获取周报异常: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/weekly")
async def submit_weekly_report(
    report_data: Dict = Body(..., description="周报数据"),
    current_user: Dict = Depends(get_current_user)
) -> Dict[str, Any]:
    """
    提交周报
    """
    try:
        # 构造云函数需要的event对象
        event = {
            "queryStringParameters": {
                "action": "submit"
            },
            "body": json.dumps(report_data)
        }
        
        # 调用云函数
        result = weekly_report_functions_handler(event, None)
        
        # 解析云函数响应
        if result.get("statusCode") == 200:
            body = json.loads(result.get("body", "{}"))
            return body
        else:
            logger.error(f"提交周报失败: {result}")
            raise HTTPException(status_code=result.get("statusCode", 500), detail="提交周报失败")
    except Exception as e:
        logger.error(f"提交周报异常: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/weekly/template")
async def get_weekly_report_template(
    project_code: str = Query(..., description="项目编号"),
    current_user: Dict = Depends(get_current_user)
) -> Dict[str, Any]:
    """
    获取周报模板
    """
    try:
        # 构造云函数需要的event对象
        event = {
            "queryStringParameters": {
                "action": "template",
                "project_code": project_code
            }
        }
        
        # 调用云函数
        result = weekly_report_functions_handler(event, None)
        
        # 解析云函数响应
        if result.get("statusCode") == 200:
            body = json.loads(result.get("body", "{}"))
            return body
        else:
            logger.error(f"获取周报模板失败: {result}")
            raise HTTPException(status_code=result.get("statusCode", 500), detail="获取周报模板失败")
    except Exception as e:
        logger.error(f"获取周报模板异常: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/weekly/export")
async def export_weekly_report(
    export_data: Dict = Body(..., description="导出数据"),
    current_user: Dict = Depends(get_current_user)
) -> Dict[str, Any]:
    """
    导出周报
    """
    try:
        # 构造云函数需要的event对象
        event = {
            "queryStringParameters": {
                "action": "export"
            },
            "body": json.dumps(export_data)
        }
        
        # 调用云函数
        result = weekly_report_functions_handler(event, None)
        
        # 解析云函数响应
        if result.get("statusCode") == 200:
            body = json.loads(result.get("body", "{}"))
            return body
        else:
            logger.error(f"导出周报失败: {result}")
            raise HTTPException(status_code=result.get("statusCode", 500), detail="导出周报失败")
    except Exception as e:
        logger.error(f"导出周报异常: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e)) 