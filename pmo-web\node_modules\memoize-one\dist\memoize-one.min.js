!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t():"function"==typeof define&&define.amd?define(t):(e="undefined"!=typeof globalThis?globalThis:e||self).memoizeOne=t()}(this,(function(){"use strict";var e=Number.isNaN||function(e){return"number"==typeof e&&e!=e};function t(t,n){if(t.length!==n.length)return!1;for(var r=0;r<t.length;r++)if(i=t[r],u=n[r],!(i===u||e(i)&&e(u)))return!1;var i,u;return!0}return function(e,n){void 0===n&&(n=t);var r=null;function i(){for(var t=[],i=0;i<arguments.length;i++)t[i]=arguments[i];if(r&&r.lastThis===this&&n(t,r.lastArgs))return r.lastResult;var u=e.apply(this,t);return r={lastResult:u,lastArgs:t,lastThis:this},u}return i.clear=function(){r=null},i}}));
