from fastapi import APIRouter, Depends, HTTPException, Query, UploadFile, File, Form
from fastapi.responses import StreamingResponse
from typing import Dict, Any, Optional, List
import json
import pandas as pd
import io
import os
from datetime import datetime
from pydantic import BaseModel

from app.core.security import get_current_user
from app.core.logger import get_logger
from app.core.database import get_db_connection

logger = get_logger(__name__)
router = APIRouter()

class ProjectModel(BaseModel):
    investment_entity: Optional[str] = None
    project_name: Optional[str] = None
    project_code: Optional[str] = None
    is_investment_plan_done: Optional[str] = None
    excellence_level: Optional[str] = None
    annual_investment_plan: Optional[float] = None
    start_time: Optional[str] = None
    acceptance_time: Optional[str] = None
    construction_content: Optional[str] = None
    project_category: Optional[str] = None
    investment_type: Optional[str] = None
    is_hardware: Optional[str] = None
    is_non_indigenous_innovation: Optional[str] = None
    is_project_established: Optional[str] = None
    project_establishment_year: Optional[str] = None
    project_planned_total_investment: Optional[float] = None
    project_establishment_name: Optional[str] = None
    software_hardware_product_procurement: Optional[float] = None
    technology_development: Optional[float] = None
    system_integration: Optional[float] = None
    others: Optional[float] = None
    line_work_special_project_work: Optional[str] = None
    responsible_department: Optional[str] = None
    project_overview: Optional[str] = None
    budget: Optional[float] = None
    business_research_time: Optional[str] = None
    solution_time: Optional[str] = None
    project_establishment_time: Optional[str] = None
    project_procurement_time: Optional[str] = None
    project_implementation_time: Optional[str] = None
    project_acceptance_time: Optional[str] = None
    progress_plan_locked: Optional[str] = None
    current_progress: Optional[str] = None
    next_steps: Optional[str] = None
    issues_to_be_coordinated_resolved: Optional[str] = None
    responsible_person: Optional[str] = None
    b_person: Optional[str] = None
    developer_1: Optional[str] = None
    developer_2: Optional[str] = None
    itbp_team_member: Optional[str] = None
    responsible_person_B: Optional[str] = None
    b_person_B: Optional[str] = None
    developer_1_B: Optional[str] = None
    developer_2_B: Optional[str] = None
    itbp_team_member_B: Optional[str] = None
    remarks: Optional[str] = None
    estimated_operating_revenue: Optional[float] = None
    estimated_project_cost: Optional[float] = None
    estimated_gross_profit: Optional[float] = None
    gross_profit_margin: Optional[float] = None
    business_number: Optional[str] = None
    project_name_1: Optional[str] = None
    category_level2: Optional[str] = None

@router.get("/projects")
async def get_projects(
    investment_entity: Optional[str] = Query(None, description="投资主体筛选"),
    project_name: Optional[str] = Query(None, description="项目名称搜索"),
    current_progress: Optional[str] = Query(None, description="当前进度筛选"),
    category_level2: Optional[str] = Query(None, description="规划类别筛选")
):
    """获取项目列表（全部显示）"""
    try:
        conn = get_db_connection()
        with conn.cursor() as cursor:
            # 构建查询条件
            where_conditions = []
            params = []

            if investment_entity:
                where_conditions.append("investment_entity = %s")
                params.append(investment_entity)

            if project_name:
                where_conditions.append("project_name LIKE %s")
                params.append(f"%{project_name}%")

            if current_progress:
                where_conditions.append("current_progress = %s")
                params.append(current_progress)

            if category_level2:
                where_conditions.append("category_level2 = %s")
                params.append(category_level2)

            where_clause = " WHERE " + " AND ".join(where_conditions) if where_conditions else ""

            # 获取所有数据
            data_sql = f"""
                SELECT * FROM project_account_book
                {where_clause}
                ORDER BY id DESC
            """
            cursor.execute(data_sql, params)
            projects = cursor.fetchall()
            total = len(projects)

        conn.close()

        return {
            "code": 200,
            "success": True,
            "message": "获取项目列表成功",
            "data": {
                "projects": projects,
                "total": total
            }
        }

    except Exception as e:
        logger.error(f"获取项目列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取项目列表失败: {str(e)}")

@router.get("/projects/{project_id}")
async def get_project_detail(project_id: int):
    """获取项目详情"""
    try:
        conn = get_db_connection()
        with conn.cursor() as cursor:
            cursor.execute("SELECT * FROM project_account_book WHERE id = %s", (project_id,))
            project = cursor.fetchone()
        
        conn.close()
        
        if not project:
            raise HTTPException(status_code=404, detail="项目不存在")
        
        return {
            "code": 200,
            "success": True,
            "message": "获取项目详情成功",
            "data": project
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取项目详情失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取项目详情失败: {str(e)}")

@router.post("/projects")
async def create_project(project: ProjectModel):
    """创建新项目"""
    try:
        conn = get_db_connection()
        with conn.cursor() as cursor:
            # 检查项目编号是否已存在
            if project.project_code:
                cursor.execute("SELECT COUNT(*) as count FROM project_account_book WHERE project_code = %s",
                             (project.project_code,))
                if cursor.fetchone()['count'] > 0:
                    raise HTTPException(status_code=400, detail="项目编号已存在")

            # 构建插入SQL
            project_dict = project.dict(exclude_none=True)
            if not project_dict:
                raise HTTPException(status_code=400, detail="没有提供有效的项目数据")

            fields = list(project_dict.keys())
            placeholders = ["%s"] * len(fields)
            values = list(project_dict.values())

            sql = f"""
                INSERT INTO project_account_book ({', '.join(fields)})
                VALUES ({', '.join(placeholders)})
            """

            cursor.execute(sql, values)
            project_id = cursor.lastrowid
            conn.commit()

        conn.close()

        return {
            "code": 200,
            "success": True,
            "message": "创建项目成功",
            "data": {"id": project_id}
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"创建项目失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"创建项目失败: {str(e)}")

@router.put("/projects/{project_id}")
async def update_project(project_id: int, project: ProjectModel):
    """更新项目信息"""
    try:
        conn = get_db_connection()
        with conn.cursor() as cursor:
            # 检查项目是否存在
            cursor.execute("SELECT COUNT(*) as count FROM project_account_book WHERE id = %s", (project_id,))
            if cursor.fetchone()['count'] == 0:
                raise HTTPException(status_code=404, detail="项目不存在")

            # 构建更新SQL
            project_dict = project.dict(exclude_none=True)
            if not project_dict:
                raise HTTPException(status_code=400, detail="没有提供有效的更新数据")

            # 如果更新项目编号，检查是否重复
            if 'project_code' in project_dict:
                cursor.execute(
                    "SELECT COUNT(*) as count FROM project_account_book WHERE project_code = %s AND id != %s",
                    (project_dict['project_code'], project_id)
                )
                if cursor.fetchone()['count'] > 0:
                    raise HTTPException(status_code=400, detail="项目编号已存在")

            set_clauses = [f"{field} = %s" for field in project_dict.keys()]
            values = list(project_dict.values()) + [project_id]

            sql = f"""
                UPDATE project_account_book
                SET {', '.join(set_clauses)}
                WHERE id = %s
            """

            cursor.execute(sql, values)
            conn.commit()

        conn.close()

        return {
            "code": 200,
            "success": True,
            "message": "更新项目成功"
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新项目失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"更新项目失败: {str(e)}")

@router.delete("/projects/{project_id}")
async def delete_project(project_id: int):
    """删除项目"""
    try:
        conn = get_db_connection()
        with conn.cursor() as cursor:
            # 检查项目是否存在
            cursor.execute("SELECT COUNT(*) as count FROM project_account_book WHERE id = %s", (project_id,))
            if cursor.fetchone()['count'] == 0:
                raise HTTPException(status_code=404, detail="项目不存在")

            cursor.execute("DELETE FROM project_account_book WHERE id = %s", (project_id,))
            conn.commit()

        conn.close()

        return {
            "code": 200,
            "success": True,
            "message": "删除项目成功"
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除项目失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"删除项目失败: {str(e)}")

@router.delete("/projects/batch")
async def batch_delete_projects(project_ids: List[int]):
    """批量删除项目"""
    try:
        if not project_ids:
            raise HTTPException(status_code=400, detail="请提供要删除的项目ID列表")

        conn = get_db_connection()
        with conn.cursor() as cursor:
            placeholders = ','.join(['%s'] * len(project_ids))
            sql = f"DELETE FROM project_account_book WHERE id IN ({placeholders})"
            cursor.execute(sql, project_ids)
            deleted_count = cursor.rowcount
            conn.commit()

        conn.close()

        return {
            "code": 200,
            "success": True,
            "message": f"成功删除 {deleted_count} 个项目"
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"批量删除项目失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"批量删除项目失败: {str(e)}")

@router.get("/projects/export/excel")
async def export_projects_excel(
    investment_entity: Optional[str] = Query(None, description="投资主体筛选"),
    project_name: Optional[str] = Query(None, description="项目名称搜索"),
    current_progress: Optional[str] = Query(None, description="当前进度筛选"),
    category_level2: Optional[str] = Query(None, description="规划类别筛选")
):
    """导出项目数据为Excel"""
    try:
        conn = get_db_connection()
        with conn.cursor() as cursor:
            # 构建查询条件
            where_conditions = []
            params = []

            if investment_entity:
                where_conditions.append("investment_entity = %s")
                params.append(investment_entity)

            if project_name:
                where_conditions.append("project_name LIKE %s")
                params.append(f"%{project_name}%")

            if current_progress:
                where_conditions.append("current_progress = %s")
                params.append(current_progress)

            if category_level2:
                where_conditions.append("category_level2 = %s")
                params.append(category_level2)

            where_clause = " WHERE " + " AND ".join(where_conditions) if where_conditions else ""

            # 获取所有数据
            sql = f"SELECT * FROM project_account_book{where_clause} ORDER BY id DESC"
            cursor.execute(sql, params)
            projects = cursor.fetchall()

        conn.close()

        if not projects:
            raise HTTPException(status_code=404, detail="没有找到符合条件的项目数据")

        # 创建DataFrame
        df = pd.DataFrame(projects)

        # 定义字段中文名映射
        column_mapping = {
            'id': 'ID',
            'investment_entity': '投资主体',
            'project_name': '项目名称',
            'project_code': '项目编号',
            'is_investment_plan_done': '是否完成投资计划',
            'excellence_level': '优秀等级',
            'annual_investment_plan': '年度投资计划',
            'start_time': '开始时间',
            'acceptance_time': '验收时间',
            'construction_content': '建设内容',
            'project_category': '项目类别',
            'investment_type': '投资类型',
            'is_hardware': '是否硬件',
            'is_non_indigenous_innovation': '是否非自主创新',
            'is_project_established': '是否立项',
            'project_establishment_year': '立项年份',
            'project_planned_total_investment': '项目计划总投资',
            'project_establishment_name': '立项名称',
            'software_hardware_product_procurement': '软硬件产品采购',
            'technology_development': '技术开发',
            'system_integration': '系统集成',
            'others': '其他',
            'line_work_special_project_work': '条线工作专项工作',
            'responsible_department': '负责部门',
            'project_overview': '项目概述',
            'budget': '预算',
            'business_research_time': '业务调研时间',
            'solution_time': '方案时间',
            'project_establishment_time': '立项时间',
            'project_procurement_time': '采购时间',
            'project_implementation_time': '实施时间',
            'project_acceptance_time': '验收时间',
            'progress_plan_locked': '进度计划锁定',
            'current_progress': '当前进度',
            'next_steps': '下一步工作',
            'issues_to_be_coordinated_resolved': '待协调解决问题',
            'responsible_person': '责任人',
            'b_person': 'B角',
            'developer_1': '开发人员1',
            'developer_2': '开发人员2',
            'itbp_team_member': 'ITBP团队成员',
            'responsible_person_B': '责任人B',
            'b_person_B': 'B角B',
            'developer_1_B': '开发人员1B',
            'developer_2_B': '开发人员2B',
            'itbp_team_member_B': 'ITBP团队成员B',
            'remarks': '备注',
            'estimated_operating_revenue': '预计营业收入',
            'estimated_project_cost': '预计项目成本',
            'estimated_gross_profit': '预计毛利润',
            'gross_profit_margin': '毛利率',
            'business_number': '业务编号',
            'project_name_1': '项目名称1',
            'category_level2': '规划类别'
        }

        # 重命名列
        df = df.rename(columns=column_mapping)

        # 创建Excel文件
        output = io.BytesIO()
        with pd.ExcelWriter(output, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name='项目数据', index=False)

        output.seek(0)

        # 生成文件名
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"项目数据导出_{timestamp}.xlsx"

        return StreamingResponse(
            io.BytesIO(output.read()),
            media_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            headers={"Content-Disposition": f"attachment; filename={filename}"}
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"导出Excel失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"导出Excel失败: {str(e)}")

@router.post("/projects/import/excel")
async def import_projects_excel(file: UploadFile = File(...)):
    """从Excel导入项目数据"""
    try:
        # 检查文件类型
        if not file.filename.endswith(('.xlsx', '.xls')):
            raise HTTPException(status_code=400, detail="请上传Excel文件(.xlsx或.xls)")

        # 读取Excel文件
        contents = await file.read()
        df = pd.read_excel(io.BytesIO(contents))

        if df.empty:
            raise HTTPException(status_code=400, detail="Excel文件为空")

        # 中文列名到英文字段名的映射（与导出时相反）
        reverse_column_mapping = {
            'ID': 'id',
            '投资主体': 'investment_entity',
            '项目名称': 'project_name',
            '项目编号': 'project_code',
            '是否完成投资计划': 'is_investment_plan_done',
            '优秀等级': 'excellence_level',
            '年度投资计划': 'annual_investment_plan',
            '开始时间': 'start_time',
            '验收时间': 'acceptance_time',
            '建设内容': 'construction_content',
            '项目类别': 'project_category',
            '投资类型': 'investment_type',
            '是否硬件': 'is_hardware',
            '是否非自主创新': 'is_non_indigenous_innovation',
            '是否立项': 'is_project_established',
            '立项年份': 'project_establishment_year',
            '项目计划总投资': 'project_planned_total_investment',
            '立项名称': 'project_establishment_name',
            '软硬件产品采购': 'software_hardware_product_procurement',
            '技术开发': 'technology_development',
            '系统集成': 'system_integration',
            '其他': 'others',
            '条线工作专项工作': 'line_work_special_project_work',
            '负责部门': 'responsible_department',
            '项目概述': 'project_overview',
            '预算': 'budget',
            '业务调研时间': 'business_research_time',
            '方案时间': 'solution_time',
            '立项时间': 'project_establishment_time',
            '采购时间': 'project_procurement_time',
            '实施时间': 'project_implementation_time',
            '验收时间': 'project_acceptance_time',
            '进度计划锁定': 'progress_plan_locked',
            '当前进度': 'current_progress',
            '下一步工作': 'next_steps',
            '待协调解决问题': 'issues_to_be_coordinated_resolved',
            '责任人': 'responsible_person',
            'B角': 'b_person',
            '开发人员1': 'developer_1',
            '开发人员2': 'developer_2',
            'ITBP团队成员': 'itbp_team_member',
            '责任人B': 'responsible_person_B',
            'B角B': 'b_person_B',
            '开发人员1B': 'developer_1_B',
            '开发人员2B': 'developer_2_B',
            'ITBP团队成员B': 'itbp_team_member_B',
            '备注': 'remarks',
            '预计营业收入': 'estimated_operating_revenue',
            '预计项目成本': 'estimated_project_cost',
            '预计毛利润': 'estimated_gross_profit',
            '毛利率': 'gross_profit_margin',
            '业务编号': 'business_number',
            '项目名称1': 'project_name_1',
            '规划类别': 'category_level2'
        }

        # 重命名列
        df = df.rename(columns=reverse_column_mapping)

        # 移除ID列（如果存在），因为ID是自增的
        if 'id' in df.columns:
            df = df.drop('id', axis=1)

        # 处理空值
        df = df.where(pd.notnull(df), None)

        conn = get_db_connection()
        success_count = 0
        error_count = 0
        errors = []

        try:
            with conn.cursor() as cursor:
                for index, row in df.iterrows():
                    try:
                        # 过滤掉空值
                        row_dict = {k: v for k, v in row.to_dict().items() if v is not None and str(v).strip() != ''}

                        if not row_dict:
                            continue

                        # 检查必要字段
                        if 'project_code' in row_dict:
                            # 检查项目编号是否已存在
                            cursor.execute("SELECT COUNT(*) as count FROM project_account_book WHERE project_code = %s",
                                         (row_dict['project_code'],))
                            if cursor.fetchone()['count'] > 0:
                                errors.append(f"第{index+2}行: 项目编号 {row_dict['project_code']} 已存在")
                                error_count += 1
                                continue

                        # 构建插入SQL
                        fields = list(row_dict.keys())
                        placeholders = ["%s"] * len(fields)
                        values = list(row_dict.values())

                        sql = f"""
                            INSERT INTO project_account_book ({', '.join(fields)})
                            VALUES ({', '.join(placeholders)})
                        """

                        cursor.execute(sql, values)
                        success_count += 1

                    except Exception as row_error:
                        error_count += 1
                        errors.append(f"第{index+2}行: {str(row_error)}")
                        continue

                conn.commit()

        finally:
            conn.close()

        return {
            "code": 200,
            "success": True,
            "message": f"导入完成：成功 {success_count} 条，失败 {error_count} 条",
            "data": {
                "success_count": success_count,
                "error_count": error_count,
                "errors": errors[:10]  # 只返回前10个错误
            }
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"导入Excel失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"导入Excel失败: {str(e)}")

@router.get("/projects/options")
async def get_project_options():
    """获取项目筛选选项"""
    try:
        conn = get_db_connection()
        with conn.cursor() as cursor:
            # 获取投资主体选项
            cursor.execute("SELECT DISTINCT investment_entity FROM project_account_book WHERE investment_entity IS NOT NULL AND investment_entity != ''")
            investment_entities = [row['investment_entity'] for row in cursor.fetchall()]

            # 获取当前进度选项
            cursor.execute("SELECT DISTINCT current_progress FROM project_account_book WHERE current_progress IS NOT NULL AND current_progress != ''")
            progress_options = [row['current_progress'] for row in cursor.fetchall()]

            # 获取规划类别选项
            cursor.execute("SELECT DISTINCT category_level2 FROM project_account_book WHERE category_level2 IS NOT NULL AND category_level2 != ''")
            category_options = [row['category_level2'] for row in cursor.fetchall()]

            # 获取项目类别选项
            cursor.execute("SELECT DISTINCT project_category FROM project_account_book WHERE project_category IS NOT NULL AND project_category != ''")
            project_category_options = [row['project_category'] for row in cursor.fetchall()]

            # 获取负责部门选项
            cursor.execute("SELECT DISTINCT responsible_department FROM project_account_book WHERE responsible_department IS NOT NULL AND responsible_department != ''")
            department_options = [row['responsible_department'] for row in cursor.fetchall()]

        conn.close()

        return {
            "code": 200,
            "success": True,
            "message": "获取筛选选项成功",
            "data": {
                "investment_entities": sorted(investment_entities),
                "progress_options": sorted(progress_options),
                "category_options": sorted(category_options),
                "project_category_options": sorted(project_category_options),
                "department_options": sorted(department_options)
            }
        }

    except Exception as e:
        logger.error(f"获取筛选选项失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取筛选选项失败: {str(e)}")

@router.get("/projects/template/excel")
async def download_excel_template():
    """下载Excel导入模板"""
    try:
        # 创建模板DataFrame
        template_data = {
            '投资主体': ['示例：财险', ''],
            '项目名称': ['示例：核心系统升级', ''],
            '项目编号': ['示例：C202500001', ''],
            '是否完成投资计划': ['示例：是', ''],
            '优秀等级': ['示例：A', ''],
            '年度投资计划': ['示例：500', ''],
            '开始时间': ['示例：2025-01-01', ''],
            '验收时间': ['示例：2025-12-31', ''],
            '建设内容': ['示例：系统功能升级改造', ''],
            '项目类别': ['示例：信息化建设', ''],
            '投资类型': ['示例：自主投资', ''],
            '是否硬件': ['示例：否', ''],
            '是否非自主创新': ['示例：否', ''],
            '是否立项': ['示例：是', ''],
            '立项年份': ['示例：2025', ''],
            '项目计划总投资': ['示例：1000', ''],
            '立项名称': ['示例：核心系统升级立项', ''],
            '软硬件产品采购': ['示例：300', ''],
            '技术开发': ['示例：400', ''],
            '系统集成': ['示例：200', ''],
            '其他': ['示例：100', ''],
            '条线工作专项工作': ['示例：信息化条线', ''],
            '负责部门': ['示例：信息技术部', ''],
            '项目概述': ['示例：提升系统性能和用户体验', ''],
            '预算': ['示例：1000', ''],
            '业务调研时间': ['示例：2025-01-15', ''],
            '方案时间': ['示例：2025-02-15', ''],
            '立项时间': ['示例：2025-03-01', ''],
            '采购时间': ['示例：2025-04-01', ''],
            '实施时间': ['示例：2025-05-01', ''],
            '验收时间': ['示例：2025-12-31', ''],
            '进度计划锁定': ['示例：否', ''],
            '当前进度': ['示例：项目立项', ''],
            '下一步工作': ['示例：开始需求调研', ''],
            '待协调解决问题': ['示例：无', ''],
            '责任人': ['示例：张三', ''],
            'B角': ['示例：李四', ''],
            '开发人员1': ['示例：王五', ''],
            '开发人员2': ['示例：赵六', ''],
            'ITBP团队成员': ['示例：孙七', ''],
            '责任人B': ['示例：', ''],
            'B角B': ['示例：', ''],
            '开发人员1B': ['示例：', ''],
            '开发人员2B': ['示例：', ''],
            'ITBP团队成员B': ['示例：', ''],
            '备注': ['示例：重点项目', ''],
            '预计营业收入': ['示例：2000', ''],
            '预计项目成本': ['示例：800', ''],
            '预计毛利润': ['示例：1200', ''],
            '毛利率': ['示例：0.6', ''],
            '业务编号': ['示例：BIZ202500001', ''],
            '项目名称1': ['示例：', ''],
            '规划类别': ['示例：基础设施建设', '']
        }

        df = pd.DataFrame(template_data)

        # 创建Excel文件
        output = io.BytesIO()
        with pd.ExcelWriter(output, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name='项目导入模板', index=False)

            # 获取工作表并设置样式
            worksheet = writer.sheets['项目导入模板']

            # 设置列宽
            for column in worksheet.columns:
                max_length = 0
                column_letter = column[0].column_letter
                for cell in column:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass
                adjusted_width = min(max_length + 2, 50)
                worksheet.column_dimensions[column_letter].width = adjusted_width

        output.seek(0)

        return StreamingResponse(
            io.BytesIO(output.read()),
            media_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            headers={"Content-Disposition": "attachment; filename=项目导入模板.xlsx"}
        )

    except Exception as e:
        logger.error(f"下载模板失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"下载模板失败: {str(e)}")
