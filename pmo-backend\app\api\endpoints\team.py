from fastapi import APIRouter, Depends, HTTPException, Query
from typing import Dict, Any, Optional
import logging
from app.core.security import get_current_user
import json
import os
import pymysql

router = APIRouter()
logger = logging.getLogger(__name__)

# 数据库配置
DB_CONFIG = {
    'host': os.environ.get('DB_HOST', 'localhost'),
    'port': int(os.environ.get('DB_PORT', 3306)),
    'user': os.environ.get('DB_USER', 'root'),
    'password': os.environ.get('DB_PASSWORD', ''),
    'database': os.environ.get('DB_NAME', 'pmo'),
    'charset': 'utf8mb4',
    'cursorclass': pymysql.cursors.DictCursor
}

# 简化版云函数调用，直接在本地执行
async def call_cloud_function(function_name, params):
    """
    简化版云函数调用，直接在本地执行
    :param function_name: 云函数名称
    :param params: 参数
    :return: 执行结果
    """
    try:
        if function_name == "itbpTeamManagement":
            from app.cloud_functions.itbpTeamManagement.index import main_handler
            result = main_handler(params, None)
            return result
        elif function_name == "user_project_management_py":
            from app.cloud_functions.user_project_management_py.index import main_handler
            result = main_handler(params, None)
            return result
        else:
            return {
                'statusCode': 400,
                'body': json.dumps({
                    'code': 400,
                    'message': f'未知的云函数: {function_name}'
                })
            }
    except Exception as e:
        logger.exception(f"调用云函数失败: {str(e)}")
        return {
            'statusCode': 500,
            'body': json.dumps({
                'code': 500,
                'message': f'调用云函数失败: {str(e)}'
            })
        }

@router.get("/members")
async def get_team_members(
    entity: str = Query(None, description="投资主体"),
    action: str = Query(None, description="操作类型"),
    search: Optional[str] = Query(None, description="搜索文本")
):
    """
    获取团队成员或用户列表
    """
    try:
        if action == "getMembers" and entity:
            logger.info(f"获取团队成员 - 参数: entity={entity}, action={action}")
            
            # 调用云函数
            params = {'queryString': {'entity': entity, 'action': action}}
            logger.info(f"调用云函数 itbpTeamManagement - 参数: {params}")
            
            result = await call_cloud_function("itbpTeamManagement", params)
            
            logger.info(f"云函数返回状态码: {result.get('statusCode')}")
            
            if result.get('statusCode') == 200:
                response_data = result.get('body', {})
                if isinstance(response_data, str):
                    import json
                    response_data = json.loads(response_data)
                
                members = response_data.get('data', {}).get('members', [])
                logger.info(f"获取到 {len(members)} 个团队成员")
                
                return response_data
            else:
                logger.error(f"获取团队成员失败: {result}")
                logger.error(f"获取团队成员异常:")
                raise HTTPException(status_code=result.get('statusCode', 500), detail=result.get('body', '未知错误'))
        
        elif action == "getUsers":
            logger.info(f"获取用户列表 - 参数: action={action}, search={search}")
            
            # 调用云函数
            params = {
                'action': 'getUsersList',
                'queryParams': {},
                'sortBy': 'UserID',  # 按UserID排序
                'sortOrder': 'asc'   # 升序排列
            }
            
            if search:
                params['queryParams']['search_text'] = search
                logger.info(f"添加搜索条件: {search}")
            
            logger.info(f"调用云函数 user_project_management_py - 参数: {params}")
            
            result = await call_cloud_function("user_project_management_py", params)
            
            logger.info(f"云函数返回状态码: {result.get('statusCode')}")
            logger.info(f"云函数返回body类型: {type(result.get('body'))}")
            
            if result.get('statusCode') == 200:
                response_data = result.get('body', {})
                logger.info(f"原始response_data类型: {type(response_data)}")
                
                if isinstance(response_data, str):
                    import json
                    try:
                        logger.info(f"尝试解析JSON字符串, 长度: {len(response_data)}")
                        logger.info(f"JSON字符串前100个字符: {response_data[:100]}")
                        response_data = json.loads(response_data, strict=False)
                        logger.info(f"JSON解析成功，解析后类型: {type(response_data)}")
                    except Exception as json_err:
                        logger.error(f"JSON解析失败: {str(json_err)}")
                        raise HTTPException(status_code=500, detail=f"JSON解析错误: {str(json_err)}")
                
                users = response_data.get('data', [])
                logger.info(f"获取到 {len(users)} 个用户")
                
                if len(users) > 0:
                    # 记录第一个用户的信息以便调试
                    first_user = users[0]
                    logger.info(f"第一个用户示例: UserID={first_user.get('UserID')}, name={first_user.get('name')}")
                    logger.info(f"用户字段: {list(first_user.keys())}")
                
                # 确保返回的JSON正确处理中文
                response = {
                    'code': 200,
                    'message': 'success',
                    'data': {
                        'users': users
                    }
                }
                
                # 返回结果前记录日志
                logger.info(f"返回用户列表成功，共 {len(users)} 个用户")
                return response
            else:
                logger.error(f"获取用户列表失败: {result}")
                raise HTTPException(status_code=result.get('statusCode', 500), detail=result.get('body', '未知错误'))
        else:
            logger.error(f"未知的操作类型: {action}")
            raise HTTPException(status_code=400, detail=f"未知的操作类型: {action}")
    except Exception as e:
        logger.exception(f"处理请求失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"服务器错误: {str(e)}")

@router.post("/members")
async def update_team_members(
    request_data: Dict[str, Any],
    current_user: Dict = Depends(get_current_user)
):
    """
    更新团队成员或用户信息
    """
    try:
        action = request_data.get('action')
        
        # 处理更新团队成员的请求
        if action == 'updateMembers':
            entity = request_data.get('entity')
            operations = request_data.get('operations')
            
            if not entity or not operations:
                raise HTTPException(status_code=400, detail="缺少必要参数")
            
            logger.info(f"更新团队成员 - 参数: entity={entity}, action={action}")
            
            # 检查operations中的ID是否为字符串，如果是则转换为整数
            if 'delete' in operations and operations['delete']:
                for i, member in enumerate(operations['delete']):
                    if 'id' in member and isinstance(member['id'], str):
                        try:
                            operations['delete'][i]['id'] = int(member['id'])
                            logger.info(f"转换删除操作的ID从字符串到整数: {member['id']}")
                        except ValueError:
                            logger.warning(f"无法将ID转换为整数: {member['id']}")
            
            if 'update' in operations and operations['update']:
                for i, member in enumerate(operations['update']):
                    if 'id' in member and isinstance(member['id'], str):
                        try:
                            operations['update'][i]['id'] = int(member['id'])
                            logger.info(f"转换更新操作的ID从字符串到整数: {member['id']}")
                        except ValueError:
                            logger.warning(f"无法将ID转换为整数: {member['id']}")
            
            # 调用云函数
            params = {
                'body': {
                    'entity': entity,
                    'action': action,
                    'operations': operations
                }
            }
            logger.info(f"调用云函数 itbpTeamManagement - 参数: {params}")
            
            result = await call_cloud_function("itbpTeamManagement", params)
        
        # 处理更新用户信息的请求
        elif action == 'updateUser':
            userData = request_data.get('userData')
            updatedBy = request_data.get('updatedBy')
            
            if not userData:
                raise HTTPException(status_code=400, detail="缺少必要参数")
            
            logger.info(f"更新用户信息 - 用户ID: {userData.get('UserID')}")
            
            # 调用云函数
            params = {
                'action': 'updateUser',
                'userData': userData,
                'updatedBy': updatedBy or current_user.get('UserID', 'admin')
            }
            logger.info(f"调用云函数 user_project_management_py - 参数: {params}")
            
            # 直接调用云函数，获取原始结果
            raw_result = await call_cloud_function("user_project_management_py", params)
            
            # 对于updateUser操作，直接返回结果，不进行额外处理
            if isinstance(raw_result, dict) and 'statusCode' not in raw_result:
                logger.info(f"更新用户成功: {raw_result}")
                return raw_result
            
            result = raw_result
        
        # 处理删除用户的请求
        elif action == 'deleteUser':
            userId = request_data.get('userId')
            adminUserId = request_data.get('adminUserId')
            
            if not userId:
                raise HTTPException(status_code=400, detail="缺少必要参数")
            
            logger.info(f"删除用户 - 用户ID: {userId}")
            
            # 调用云函数
            params = {
                'action': 'deleteUser',
                'userId': userId,
                'adminUserId': adminUserId or current_user.get('UserID', 'admin')
            }
            logger.info(f"调用云函数 user_project_management_py - 参数: {params}")
            
            # 直接调用云函数，获取原始结果
            raw_result = await call_cloud_function("user_project_management_py", params)
            
            # 对于deleteUser操作，直接返回结果，不进行额外处理
            if isinstance(raw_result, dict) and 'statusCode' not in raw_result:
                logger.info(f"删除用户成功: {raw_result}")
                return raw_result
            
            result = raw_result
        
        else:
            raise HTTPException(status_code=400, detail=f"不支持的操作类型: {action}")
        
        logger.info(f"云函数返回状态码: {result.get('statusCode')}")
        
        if result.get('statusCode') == 200:
            response_data = result.get('body', {})
            if isinstance(response_data, str):
                import json
                response_data = json.loads(response_data)
            
            return response_data
        else:
            logger.error(f"操作失败: {result}")
            raise HTTPException(status_code=result.get('statusCode', 500), detail=result.get('body', '未知错误'))
    except HTTPException:
        raise
    except Exception as e:
        logger.exception(f"处理请求失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"服务器错误: {str(e)}") 