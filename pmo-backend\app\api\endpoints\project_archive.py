#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
项目档案管理API
"""

import os
import uuid
import json
import shutil
import re
from datetime import datetime
from typing import Dict, List, Optional
from fastapi import APIRouter, Depends, HTTPException, UploadFile, File, Form, BackgroundTasks, Request, Body
from fastapi.responses import JSONResponse, FileResponse
from fastapi.exceptions import RequestValidationError
from pydantic import BaseModel, ValidationError

from app.core.security import get_current_user
from app.core.logger import get_logger
from app.db.connection import get_db_connection
from app.services.archive_classifier import ProjectArchiveClassifier
from app.services.ai_chat import AIChatService
from app.services.document_parser import DocumentParser
from app.services.file_storage import FileStorageService

logger = get_logger(__name__)
router = APIRouter()

# 初始化服务 - 延迟导入避免循环依赖
archive_classifier = None
file_storage = FileStorageService()

# 分类结果缓存和任务状态缓存
results_cache = {}
task_cache = {}

# 预定义的档案清单（替代数据库表）
DEFAULT_ARCHIVE_CHECKLIST = [
    {"main_process": "项目立项", "sub_process": "立项报告", "output_name": "立项申请书", "content_description": "项目立项申请文件", "keywords": "立项申请,项目申请,立项报告"},
    {"main_process": "项目立项", "sub_process": "立项报告", "output_name": "可行性研究报告", "content_description": "项目可行性分析文件", "keywords": "可行性研究,可行性分析,研究报告"},
    {"main_process": "项目立项", "sub_process": "立项报告", "output_name": "项目建议书", "content_description": "项目建议文件", "keywords": "项目建议,建议书,项目提案"},
    {"main_process": "项目设计", "sub_process": "需求分析", "output_name": "需求规格说明书", "content_description": "项目需求分析文件", "keywords": "需求规格,需求分析,需求说明"},
    {"main_process": "项目设计", "sub_process": "系统设计", "output_name": "系统设计方案", "content_description": "系统设计文件", "keywords": "系统设计,设计方案,架构设计"},
    {"main_process": "项目设计", "sub_process": "技术方案", "output_name": "技术实施方案", "content_description": "技术方案文件", "keywords": "技术方案,实施方案,技术设计"},
    {"main_process": "项目采购", "sub_process": "采购文件", "output_name": "采购合同", "content_description": "采购合同文件", "keywords": "采购合同,合同文件,采购协议"},
    {"main_process": "项目采购", "sub_process": "采购文件", "output_name": "招标文件", "content_description": "招标相关文件", "keywords": "招标文件,招标公告,投标文件"},
    {"main_process": "项目实施", "sub_process": "开发文档", "output_name": "开发计划", "content_description": "项目开发计划", "keywords": "开发计划,项目计划,实施计划"},
    {"main_process": "项目实施", "sub_process": "测试文档", "output_name": "测试报告", "content_description": "项目测试文件", "keywords": "测试报告,测试文档,测试结果"},
    {"main_process": "项目验收", "sub_process": "验收文档", "output_name": "验收报告", "content_description": "项目验收文件", "keywords": "验收报告,验收文档,项目验收"},
    {"main_process": "项目验收", "sub_process": "验收文档", "output_name": "用户手册", "content_description": "用户使用手册", "keywords": "用户手册,使用手册,操作手册"},
    {"main_process": "项目运维", "sub_process": "运维文档", "output_name": "运维手册", "content_description": "系统运维文件", "keywords": "运维手册,维护手册,运维文档"},
    {"main_process": "项目运维", "sub_process": "运维文档", "output_name": "维护记录", "content_description": "系统维护记录", "keywords": "维护记录,运维记录,维护日志"}
]

def get_archive_classifier():
    """获取档案分类器实例"""
    global archive_classifier
    if archive_classifier is None:
        from app.api.endpoints.ai_assistant import ai_chat_service, document_parser
        archive_classifier = ProjectArchiveClassifier(ai_chat_service, document_parser)
    return archive_classifier

def parse_batch_classification_result(ai_response: str, files: List[Dict]) -> List[Dict]:
    """解析AI批量分类结果"""
    try:
        import json
        import re

        # 尝试提取JSON部分
        json_match = re.search(r'\{.*\}', ai_response, re.DOTALL)
        if json_match:
            json_str = json_match.group()
            result = json.loads(json_str)

            if "classifications" in result:
                return result["classifications"]

        # 如果解析失败，返回默认分类
        logger.warning("AI批量分类结果解析失败，使用默认分类")
        return create_fallback_classifications(files)

    except Exception as e:
        logger.error(f"解析AI分类结果失败: {str(e)}")
        return create_fallback_classifications(files)

def create_fallback_classifications(files: List[Dict]) -> List[Dict]:
    """创建默认分类结果"""
    classifications = []
    for file_data in files:
        classifications.append({
            "filename": file_data["filename"],
            "main_process": "未分类",
            "sub_process": "未分类",
            "output_name": "未分类文档",
            "confidence": 0.0,
            "reason": "AI分类失败，使用默认分类"
        })
    return classifications

def generate_suggested_filename(original_filename: str, classification: Dict) -> str:
    """生成建议文件名"""
    try:
        # 获取文件扩展名
        import os
        name, ext = os.path.splitext(original_filename)

        # 基于分类结果生成建议文件名
        output_name = classification.get("output_name", "未分类文档")
        suggested_name = f"{output_name}_{name}{ext}"

        return suggested_name
    except:
        return original_filename

def generate_storage_path(project_code: str, classification: Dict) -> str:
    """生成存储路径"""
    try:
        main_process = classification.get("main_process", "未分类")
        sub_process = classification.get("sub_process", "未分类")

        # 清理路径中的特殊字符
        main_process = re.sub(r'[<>:"/\\|?*]', '_', main_process)
        sub_process = re.sub(r'[<>:"/\\|?*]', '_', sub_process)

        return f"project_archive_materials/{project_code}/{main_process}/{sub_process}/"
    except:
        return f"project_archive_materials/{project_code}/未分类/"

# 数据模型
class FileClassificationResult(BaseModel):
    filename: str
    original_filename: str
    main_process: str
    sub_process: str
    output_name: str
    confidence: float
    reason: str
    suggested_filename: str
    storage_path: str
    file_size: int
    error: Optional[bool] = False
    # 新增字段：项目信息识别和匹配
    detected_investor: Optional[str] = ""
    detected_project: Optional[str] = ""
    investor_match: Optional[bool] = None
    project_match: Optional[bool] = None
    # 新增字段：解析内容相关
    parsed_content_path: Optional[str] = ""
    parsed_content_preview: Optional[str] = ""

class ClassificationConfirmation(BaseModel):
    filename: str
    main_process: str
    sub_process: str
    output_name: str
    confirmed: bool = True

class BatchConfirmationRequest(BaseModel):
    task_id: str
    confirmations: List[ClassificationConfirmation]

# 轻量级档案数量统计函数 - 专门用于项目管理页面
def get_project_archive_count(project_code: str) -> Dict:
    """获取项目档案文件数量统计，专门用于项目管理页面显示"""
    try:
        import os

        # 项目档案目录
        archive_base_dir = "project_archive_materials"
        project_dir = os.path.join(archive_base_dir, project_code)

        if not os.path.exists(project_dir):
            return {
                "total_files": 0,
                "markdown_files": 0,
                "other_files": 0,
                "archive_summary": "暂无档案"
            }

        total_files = 0
        markdown_files = 0
        other_files = 0

        # 遍历项目目录统计文件
        for root, dirs, files in os.walk(project_dir):
            for file in files:
                if file.endswith(('.pdf', '.doc', '.docx', '.md', '.txt', '.xlsx', '.xls', '.jpg', '.png')):
                    total_files += 1
                    if file.endswith('.md'):
                        markdown_files += 1
                    else:
                        other_files += 1

        # 生成摘要
        if total_files == 0:
            archive_summary = "暂无档案"
        elif markdown_files == 0:
            archive_summary = f"共{total_files}个文件"
        else:
            archive_summary = f"共{total_files}个文件(md:{markdown_files})"

        return {
            "total_files": total_files,
            "markdown_files": markdown_files,
            "other_files": other_files,
            "archive_summary": archive_summary
        }

    except Exception as e:
        logger.error(f"获取项目 {project_code} 档案数量失败: {str(e)}")
        return {
            "total_files": 0,
            "markdown_files": 0,
            "other_files": 0,
            "archive_summary": "获取失败"
        }

# 档案状态检查函数 - 基于文件系统，按阶段统计
def get_project_archive_status_from_files(project_code: str) -> Dict:
    """基于文件系统获取项目的档案状态，按阶段分组统计"""
    try:
        import os
        from datetime import datetime

        # 项目档案目录
        archive_base_dir = "project_archive_materials"
        project_dir = os.path.join(archive_base_dir, project_code)

        if not os.path.exists(project_dir):
            return {
                "total_files": 0,
                "total_size": 0,
                "stages": {},
                "latest_file_time": None,
                "archive_summary": "暂无档案文件"
            }

        # 按阶段统计文件信息
        stages = {}
        total_files = 0
        total_size = 0
        latest_file_time = None

        # 遍历项目目录
        for root, dirs, files in os.walk(project_dir):
            # 获取相对路径，确定阶段
            rel_path = os.path.relpath(root, project_dir)

            # 解析目录结构获取阶段信息
            if rel_path == ".":
                stage_name = "根目录"
            else:
                # 取第一级目录作为阶段名
                stage_name = rel_path.split(os.sep)[0]

            if stage_name not in stages:
                stages[stage_name] = {
                    "stage_name": stage_name,
                    "file_count": 0,
                    "total_size": 0,
                    "file_types": {},
                    "latest_file_time": None,
                    "folder_path": os.path.join(project_code, stage_name) if stage_name != "根目录" else project_code
                }

            # 统计当前目录的文件
            for file in files:
                if file.endswith(('.pdf', '.doc', '.docx', '.md', '.txt', '.xlsx', '.xls', '.jpg', '.png')):
                    file_path = os.path.join(root, file)
                    try:
                        stat = os.stat(file_path)
                        file_size = stat.st_size
                        file_mtime = stat.st_mtime

                        # 更新阶段统计
                        stages[stage_name]["file_count"] += 1
                        stages[stage_name]["total_size"] += file_size

                        # 统计文件类型
                        ext = os.path.splitext(file)[1].lower()
                        stages[stage_name]["file_types"][ext] = stages[stage_name]["file_types"].get(ext, 0) + 1

                        # 更新最新文件时间
                        if stages[stage_name]["latest_file_time"] is None or file_mtime > stages[stage_name]["latest_file_time"]:
                            stages[stage_name]["latest_file_time"] = file_mtime

                        # 更新全局统计
                        total_files += 1
                        total_size += file_size
                        if latest_file_time is None or file_mtime > latest_file_time:
                            latest_file_time = file_mtime

                    except Exception as e:
                        logger.warning(f"读取文件信息失败 {file_path}: {str(e)}")

        # 格式化时间
        if latest_file_time:
            latest_file_time = datetime.fromtimestamp(latest_file_time).strftime('%Y-%m-%d %H:%M:%S')

        for stage in stages.values():
            if stage["latest_file_time"]:
                stage["latest_file_time"] = datetime.fromtimestamp(stage["latest_file_time"]).strftime('%Y-%m-%d %H:%M:%S')

        # 生成档案摘要
        if total_files == 0:
            archive_summary = "暂无档案文件"
        elif total_files < 5:
            archive_summary = "档案较少"
        elif total_files < 15:
            archive_summary = "档案适中"
        else:
            archive_summary = "档案丰富"

        return {
            "total_files": total_files,
            "total_size": total_size,
            "stages": stages,
            "latest_file_time": latest_file_time,
            "archive_summary": archive_summary
        }

    except Exception as e:
        logger.error(f"获取项目档案状态失败 {project_code}: {str(e)}")
        return {
            "total_files": 0,
            "total_size": 0,
            "stages": {},
            "latest_file_time": None,
            "archive_summary": "获取失败"
        }

# 移除了错误的进度推测函数 auto_update_project_progress
# 项目进度应该由项目管理人员手动更新，而不是根据文件数量自动推测

@router.get("/projects")
async def get_projects():
    """获取所有项目列表"""
    try:
        conn = get_db_connection()
        with conn.cursor() as cursor:
            cursor.execute("""
                SELECT DISTINCT 
                    project_code,
                    project_name,
                    investment_entity,
                    current_progress,
                    responsible_person
                FROM project_account_book 
                WHERE project_code IS NOT NULL 
                AND project_name IS NOT NULL
                ORDER BY project_name
            """)
            projects = cursor.fetchall()
        
        conn.close()
        
        return {
            "code": 200,
            "success": True,
            "message": "获取项目列表成功",
            "data": projects,
            "total": len(projects)
        }
        
    except Exception as e:
        logger.error(f"获取项目列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取项目列表失败: {str(e)}")

@router.get("/projects/status")
async def get_projects_archive_status():
    """获取所有项目的档案状态"""
    try:
        conn = get_db_connection()
        with conn.cursor() as cursor:
            # 获取所有项目
            cursor.execute("""
                SELECT DISTINCT
                    project_code,
                    project_name,
                    investment_entity,
                    current_progress,
                    responsible_person
                FROM project_account_book
                WHERE project_code IS NOT NULL
                AND project_name IS NOT NULL
                ORDER BY project_name
            """)
            projects = cursor.fetchall()

            projects_with_status = []
            for project in projects:
                try:
                    # 获取每个项目的档案状态
                    archive_status = get_project_archive_status_from_files(project['project_code'])
                    project_data = dict(project)
                    project_data['archive_status'] = archive_status
                    projects_with_status.append(project_data)
                except Exception as e:
                    logger.error(f"获取项目 {project.get('project_code')} 档案状态失败: {str(e)}")
                    # 添加默认的档案状态
                    project_data = dict(project)
                    project_data['archive_status'] = {
                        "total_files": 0,
                        "total_size": 0,
                        "file_count_by_type": {},
                        "latest_file_time": None,
                        "archive_summary": "获取失败"
                    }
                    projects_with_status.append(project_data)

        conn.close()

        return {
            "code": 200,
            "success": True,
            "message": "获取项目档案状态成功",
            "data": projects_with_status,
            "total": len(projects_with_status)
        }
    except Exception as e:
        logger.error(f"获取项目档案状态失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取项目档案状态失败: {str(e)}")

@router.get("/projects/{project_code}/folder/{folder_path:path}")
async def get_project_folder_contents(project_code: str, folder_path: str):
    """获取项目文件夹内容"""
    try:
        import os
        from datetime import datetime

        # 项目档案目录
        archive_base_dir = "project_archive_materials"

        # 构建完整路径
        if folder_path == project_code:
            # 根目录
            full_path = os.path.join(archive_base_dir, project_code)
        else:
            # 子目录
            full_path = os.path.join(archive_base_dir, folder_path)

        if not os.path.exists(full_path):
            return {
                "success": False,
                "message": "文件夹不存在",
                "data": {
                    "folder_path": folder_path,
                    "files": [],
                    "folders": []
                }
            }

        files = []
        folders = []

        # 遍历目录内容
        for item in os.listdir(full_path):
            item_path = os.path.join(full_path, item)

            if os.path.isfile(item_path):
                # 文件
                try:
                    stat = os.stat(item_path)
                    ext = os.path.splitext(item)[1].lower()

                    # 只显示文档文件
                    if ext in ['.pdf', '.doc', '.docx', '.md', '.txt', '.xlsx', '.xls', '.jpg', '.png']:
                        files.append({
                            "name": item,
                            "size": stat.st_size,
                            "modified_time": datetime.fromtimestamp(stat.st_mtime).strftime('%Y-%m-%d %H:%M:%S'),
                            "type": ext,
                            "path": os.path.join(folder_path, item).replace('\\', '/')
                        })
                except Exception as e:
                    logger.warning(f"读取文件信息失败 {item_path}: {str(e)}")

            elif os.path.isdir(item_path):
                # 文件夹
                try:
                    # 统计文件夹内文件数量
                    file_count = 0
                    for root, dirs, files_in_dir in os.walk(item_path):
                        for file in files_in_dir:
                            if file.endswith(('.pdf', '.doc', '.docx', '.md', '.txt', '.xlsx', '.xls', '.jpg', '.png')):
                                file_count += 1

                    folders.append({
                        "name": item,
                        "file_count": file_count,
                        "path": os.path.join(folder_path, item).replace('\\', '/')
                    })
                except Exception as e:
                    logger.warning(f"读取文件夹信息失败 {item_path}: {str(e)}")

        # 按名称排序
        files.sort(key=lambda x: x['name'])
        folders.sort(key=lambda x: x['name'])

        return {
            "success": True,
            "message": "获取文件夹内容成功",
            "data": {
                "folder_path": folder_path,
                "files": files,
                "folders": folders,
                "total_files": len(files),
                "total_folders": len(folders)
            }
        }

    except Exception as e:
        logger.error(f"获取文件夹内容失败 {project_code}/{folder_path}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取文件夹内容失败: {str(e)}")

@router.get("/projects/{project_code}/file/{file_path:path}")
async def get_project_file_content(project_code: str, file_path: str):
    """获取项目文件内容"""
    try:
        import os

        # 项目档案目录
        archive_base_dir = "project_archive_materials"

        # 构建完整文件路径
        full_file_path = os.path.join(archive_base_dir, file_path)

        if not os.path.exists(full_file_path):
            return {
                "success": False,
                "message": "文件不存在",
                "data": None
            }

        if not os.path.isfile(full_file_path):
            return {
                "success": False,
                "message": "路径不是文件",
                "data": None
            }

        # 获取文件信息
        file_stat = os.stat(full_file_path)
        file_ext = os.path.splitext(full_file_path)[1].lower()

        # 读取文件内容
        content = None
        content_type = "unknown"

        try:
            if file_ext in ['.txt', '.md']:
                # 文本文件
                with open(full_file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                content_type = "text"
            elif file_ext in ['.pdf']:
                # PDF文件，返回base64编码
                with open(full_file_path, 'rb') as f:
                    import base64
                    content = base64.b64encode(f.read()).decode('utf-8')
                content_type = "pdf"
            elif file_ext in ['.jpg', '.jpeg', '.png', '.gif']:
                # 图片文件，返回base64编码
                with open(full_file_path, 'rb') as f:
                    import base64
                    content = base64.b64encode(f.read()).decode('utf-8')
                content_type = "image"
            elif file_ext in ['.doc', '.docx', '.xlsx', '.xls']:
                # Office文件，提示下载
                content_type = "office"
                content = "此文件类型需要下载查看"
            else:
                # 其他文件类型
                content_type = "binary"
                content = "不支持预览此文件类型"

        except Exception as e:
            logger.warning(f"读取文件内容失败 {full_file_path}: {str(e)}")
            content = f"读取文件失败: {str(e)}"
            content_type = "error"

        return {
            "success": True,
            "message": "获取文件内容成功",
            "data": {
                "file_path": file_path,
                "file_name": os.path.basename(full_file_path),
                "file_size": file_stat.st_size,
                "file_type": file_ext,
                "content_type": content_type,
                "content": content,
                "modified_time": datetime.fromtimestamp(file_stat.st_mtime).strftime('%Y-%m-%d %H:%M:%S')
            }
        }

    except Exception as e:
        logger.error(f"获取文件内容失败 {project_code}/{file_path}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取文件内容失败: {str(e)}")

@router.get("/projects/{project_code}/file/{file_path:path}/download")
async def download_project_file(
    project_code: str,
    file_path: str
):
    """下载项目文件"""
    try:
        import os

        # 项目档案目录
        archive_base_dir = "project_archive_materials"

        # 构建完整文件路径
        full_file_path = os.path.join(archive_base_dir, file_path)

        # 调试信息
        logger.info(f"下载文件请求 - 项目编号: {project_code}, 文件路径: {file_path}")
        logger.info(f"完整文件路径: {full_file_path}")
        logger.info(f"文件是否存在: {os.path.exists(full_file_path)}")

        if not os.path.exists(full_file_path):
            raise HTTPException(status_code=404, detail="文件不存在")

        if not os.path.isfile(full_file_path):
            raise HTTPException(status_code=400, detail="路径不是文件")

        # 获取文件名
        filename = os.path.basename(full_file_path)

        # 返回文件下载响应
        return FileResponse(
            path=full_file_path,
            filename=filename,
            media_type='application/octet-stream'
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"下载项目文件失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"下载项目文件失败: {str(e)}")

@router.delete("/projects/{project_code}/file/{file_path:path}")
async def delete_project_file(project_code: str, file_path: str):
    """删除项目文件"""
    try:
        import os

        # 项目档案目录
        archive_base_dir = "project_archive_materials"

        # 构建完整文件路径
        full_file_path = os.path.join(archive_base_dir, file_path)

        if not os.path.exists(full_file_path):
            return {
                "success": False,
                "message": "文件不存在"
            }

        if not os.path.isfile(full_file_path):
            return {
                "success": False,
                "message": "路径不是文件"
            }

        # 删除文件
        os.remove(full_file_path)

        logger.info(f"文件删除成功: {full_file_path}")

        return {
            "success": True,
            "message": "文件删除成功"
        }

    except Exception as e:
        logger.error(f"删除文件失败 {project_code}/{file_path}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"删除文件失败: {str(e)}")

@router.post("/projects/{project_code}/folder/{folder_path:path}/upload")
async def upload_file_to_folder(
    project_code: str,
    folder_path: str,
    files: List[UploadFile] = File(...)
):
    """上传文件到指定文件夹"""
    try:
        import os
        import shutil

        # 项目档案目录
        archive_base_dir = "project_archive_materials"

        # 构建目标文件夹路径
        target_folder = os.path.join(archive_base_dir, folder_path)

        # 确保目标文件夹存在
        os.makedirs(target_folder, exist_ok=True)

        uploaded_files = []
        failed_files = []

        for file in files:
            try:
                # 构建文件路径
                file_path = os.path.join(target_folder, file.filename)

                # 检查文件是否已存在
                if os.path.exists(file_path):
                    failed_files.append({
                        "filename": file.filename,
                        "error": "文件已存在"
                    })
                    continue

                # 保存文件
                with open(file_path, "wb") as buffer:
                    shutil.copyfileobj(file.file, buffer)

                # 获取文件信息
                file_stat = os.stat(file_path)

                uploaded_files.append({
                    "filename": file.filename,
                    "size": file_stat.st_size,
                    "path": os.path.join(folder_path, file.filename).replace('\\', '/'),
                    "modified_time": datetime.fromtimestamp(file_stat.st_mtime).strftime('%Y-%m-%d %H:%M:%S')
                })

                logger.info(f"文件上传成功: {file_path}")

            except Exception as e:
                logger.error(f"上传文件失败 {file.filename}: {str(e)}")
                failed_files.append({
                    "filename": file.filename,
                    "error": str(e)
                })

        return {
            "success": True,
            "message": f"上传完成，成功 {len(uploaded_files)} 个，失败 {len(failed_files)} 个",
            "data": {
                "uploaded_files": uploaded_files,
                "failed_files": failed_files,
                "total_uploaded": len(uploaded_files),
                "total_failed": len(failed_files)
            }
        }

    except Exception as e:
        logger.error(f"上传文件到文件夹失败 {project_code}/{folder_path}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"上传文件失败: {str(e)}")

@router.get("/projects/{project_code}")
async def get_project_detail(project_code: str, current_user: dict = Depends(get_current_user)):
    """获取项目详细信息"""
    try:
        conn = get_db_connection()
        with conn.cursor() as cursor:
            cursor.execute("""
                SELECT * FROM project_account_book 
                WHERE project_code = %s
            """, (project_code,))
            project = cursor.fetchone()
            
            if not project:
                raise HTTPException(status_code=404, detail="项目不存在")
        
        conn.close()
        
        return {
            "success": True,
            "data": project
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取项目详情失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取项目详情失败: {str(e)}")

@router.get("/archive-checklist")
async def get_archive_checklist():
    """获取项目档案清单"""
    try:
        # 返回标准的项目管理档案清单
        checklist_data = [
            # 项目启动阶段
            {"main_process": "项目启动", "sub_process": "项目立项", "output_name": "项目建议书"},
            {"main_process": "项目启动", "sub_process": "项目立项", "output_name": "可行性研究报告"},
            {"main_process": "项目启动", "sub_process": "项目立项", "output_name": "项目章程"},

            # 项目规划阶段
            {"main_process": "项目规划", "sub_process": "范围管理", "output_name": "需求规格说明书"},
            {"main_process": "项目规划", "sub_process": "范围管理", "output_name": "WBS工作分解结构"},
            {"main_process": "项目规划", "sub_process": "进度管理", "output_name": "项目进度计划"},
            {"main_process": "项目规划", "sub_process": "成本管理", "output_name": "项目预算"},
            {"main_process": "项目规划", "sub_process": "质量管理", "output_name": "质量管理计划"},
            {"main_process": "项目规划", "sub_process": "人力资源管理", "output_name": "项目组织架构"},
            {"main_process": "项目规划", "sub_process": "沟通管理", "output_name": "沟通管理计划"},
            {"main_process": "项目规划", "sub_process": "风险管理", "output_name": "风险管理计划"},

            # 项目执行阶段
            {"main_process": "项目执行", "sub_process": "系统设计", "output_name": "系统架构设计"},
            {"main_process": "项目执行", "sub_process": "系统设计", "output_name": "详细设计文档"},
            {"main_process": "项目执行", "sub_process": "系统开发", "output_name": "开发代码"},
            {"main_process": "项目执行", "sub_process": "系统测试", "output_name": "测试用例"},
            {"main_process": "项目执行", "sub_process": "系统测试", "output_name": "测试报告"},

            # 项目监控阶段
            {"main_process": "项目监控", "sub_process": "进度监控", "output_name": "项目状态报告"},
            {"main_process": "项目监控", "sub_process": "质量监控", "output_name": "质量检查报告"},
            {"main_process": "项目监控", "sub_process": "变更管理", "output_name": "变更申请单"},

            # 项目收尾阶段
            {"main_process": "项目收尾", "sub_process": "系统部署", "output_name": "部署文档"},
            {"main_process": "项目收尾", "sub_process": "用户培训", "output_name": "培训材料"},
            {"main_process": "项目收尾", "sub_process": "项目验收", "output_name": "验收报告"},
            {"main_process": "项目收尾", "sub_process": "项目总结", "output_name": "项目总结报告"}
        ]

        return {
            "code": 200,
            "success": True,
            "message": "获取档案清单成功",
            "data": checklist_data,
            "total": len(checklist_data)
        }
    except Exception as e:
        logger.error(f"获取档案清单失败: {str(e)}")
        return {
            "code": 500,
            "success": False,
            "message": f"获取档案清单失败: {str(e)}",
            "data": [],
            "total": 0
        }

@router.post("/projects/{project_code}/upload-files")
async def upload_project_files(
    background_tasks: BackgroundTasks,
    project_code: str,
    files: List[UploadFile] = File(...),
    project_name: str = Form(""),
    investment_entity: str = Form(""),
    current_user: dict = Depends(get_current_user)
):
    """上传项目文件并进行AI分类"""
    try:
        logger.info(f"接收到上传请求 - 项目编号: {project_code}")
        logger.info(f"前端传递的项目名称: '{project_name}'")
        logger.info(f"前端传递的投资主体: '{investment_entity}'")

        # 验证项目是否存在
        conn = get_db_connection()
        with conn.cursor() as cursor:
            cursor.execute("SELECT project_name, investment_entity FROM project_account_book WHERE project_code = %s", (project_code,))
            project = cursor.fetchone()
            if not project:
                raise HTTPException(status_code=404, detail="项目不存在")
            # 保存从数据库获取的项目信息
            db_project_name = project['project_name']
            db_investment_entity = project['investment_entity']
        conn.close()

        # 使用前端传来的项目信息作为期望值，如果为空则使用数据库中的值
        expected_project_name = project_name if project_name else db_project_name
        expected_investment_entity = investment_entity if investment_entity else db_investment_entity
        
        # 生成任务ID
        task_id = str(uuid.uuid4())
        
        # 创建分类任务记录（内存缓存）
        from datetime import datetime
        task_cache[task_id] = {
            "task_id": task_id,
            "project_code": project_code,
            "total_files": len(files),
            "processed_files": 0,
            "status": "processing",
            "created_by": current_user.get('username', 'unknown'),
            "created_at": datetime.now().isoformat(),
            "error_message": None
        }
        


        # 启动后台任务进行逐个文件处理
        background_tasks.add_task(
            process_files_classification_sequential,
            task_id, project_code, db_project_name, files, current_user, expected_project_name, expected_investment_entity
        )
        
        return {
            "code": 200,
            "success": True,
            "message": f"开始处理 {len(files)} 个文件，任务ID: {task_id}",
            "data": {
                "task_id": task_id,
                "project_code": project_code,
                "files_count": len(files),
                "files": [{"filename": f.filename, "size": f.size} for f in files]
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"文件上传失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"文件上传失败: {str(e)}")

async def process_files_classification_sequential(
    task_id: str,
    project_code: str,
    project_name: str,
    files: List[UploadFile],
    current_user: dict,
    expected_project_name: str = "",
    expected_investment_entity: str = ""
):
    """逐个处理文件解析和AI分类"""
    try:
        logger.info(f"开始逐个处理分类任务: {task_id}, 项目: {project_code}, 文件数: {len(files)}")
        logger.info(f"接收到的期望项目名称: '{expected_project_name}', 期望投资主体: '{expected_investment_entity}'")

        # 获取档案清单（使用预定义清单）
        checklist = DEFAULT_ARCHIVE_CHECKLIST

        # 导入必要的模块
        from app.api.endpoints.ai_assistant import document_parser
        classifier = get_archive_classifier()
        classification_results = []
        processed_count = 0

        # 逐个处理文件
        for file_index, file in enumerate(files):
            try:
                logger.info(f"开始处理文件 {file_index + 1}/{len(files)}: {file.filename}")

                # 1. 读取文件内容
                file_content = await file.read()

                # 2. 验证文件大小（限制为20MB）
                if len(file_content) > 20 * 1024 * 1024:
                    result = FileClassificationResult(
                        filename=file.filename,
                        original_filename=file.filename,
                        main_process="未分类",
                        sub_process="未分类",
                        output_name="文件过大",
                        confidence=0.0,
                        reason="文件大小超过20MB限制",
                        suggested_filename=file.filename,
                        storage_path="",
                        file_size=len(file_content),
                        error=True,
                        detected_investor="",
                        detected_project="",
                        investor_match=None,
                        project_match=None
                    )
                    classification_results.append(result.dict())
                    processed_count += 1

                    # 更新进度（内存缓存）
                    if task_id in task_cache:
                        task_cache[task_id]["processed_files"] = processed_count

                    logger.warning(f"文件过大跳过: {file.filename}")
                    continue

                # 3. 解析文档内容
                logger.info(f"开始解析文档: {file.filename}")
                parsed_content = document_parser.parse_document(file_content, file.filename, file.content_type)

                # 4. 处理异步解析（图片、PDF等）
                if parsed_content.startswith("IMAGE_TO_PROCESS:"):
                    image_base64 = parsed_content.replace("IMAGE_TO_PROCESS:", "")
                    try:
                        parsed_content = await document_parser.parse_image_with_vision_model(image_base64)
                        logger.info(f"图片解析完成: {file.filename}")
                    except Exception as e:
                        parsed_content = f"图片解析失败: {str(e)}"
                        logger.error(f"图片解析失败: {file.filename} - {str(e)}")

                elif parsed_content.startswith("PDF_TO_PROCESS:"):
                    logger.info(f"检测到PDF文件需要异步处理: {file.filename}")
                    pdf_base64 = parsed_content.replace("PDF_TO_PROCESS:", "")
                    try:
                        import base64
                        pdf_bytes = base64.b64decode(pdf_base64)
                        parsed_content = await document_parser.parse_pdf_file(pdf_bytes)
                        logger.info(f"PDF解析完成: {file.filename}, 内容长度: {len(parsed_content)}")
                    except Exception as e:
                        parsed_content = f"PDF解析失败: {str(e)}"
                        logger.error(f"PDF解析失败: {file.filename} - {str(e)}")

                elif parsed_content.startswith("PPT_WITH_IMAGES:"):
                    try:
                        parsed_content = await document_parser.process_ppt_with_images(parsed_content)
                        logger.info(f"PPT解析完成: {file.filename}")
                    except Exception as e:
                        parsed_content = f"PPT解析失败: {str(e)}"
                        logger.error(f"PPT解析失败: {file.filename} - {str(e)}")

                # 5. 使用AI进行分类（使用已解析的内容）
                logger.info(f"开始AI分类: {file.filename}")
                try:
                    classification = await classifier.classify_parsed_content(
                        parsed_content, file.filename, checklist, expected_project_name, expected_investment_entity
                    )
                    logger.info(f"AI分类完成: {file.filename} -> {classification.get('output_name', 'Unknown')}")
                except Exception as ai_error:
                    logger.error(f"AI分类失败: {file.filename} - {str(ai_error)}")
                    classification = {
                        "main_process": "未分类",
                        "sub_process": "未分类",
                        "output_name": "AI分类失败",
                        "confidence": 0.0,
                        "reason": f"AI分类失败: {str(ai_error)}",
                        "error": True
                    }

                # 6. 保存临时文件
                temp_file_path = file_storage.save_temp_file(task_id, file.filename, file_content)

                # 6.1 保存解析内容（如果有的话）
                temp_parsed_content_path = ""
                if parsed_content and len(parsed_content.strip()) > 0:
                    temp_parsed_content_path = file_storage.save_temp_parsed_content(
                        task_id, file.filename, parsed_content
                    )

                # 7. 生成建议文件名和存储路径
                suggested_filename = classifier.get_suggested_filename(file.filename, classification)
                storage_path = classifier.get_storage_path(project_code, classification)

                # 8. 构建结果
                result = FileClassificationResult(
                    filename=file.filename,
                    original_filename=file.filename,
                    main_process=classification.get("main_process", "未分类"),
                    sub_process=classification.get("sub_process", "未分类"),
                    output_name=classification.get("output_name", "未分类文档"),
                    confidence=classification.get("confidence", 0.0),
                    reason=classification.get("reason", ""),
                    suggested_filename=suggested_filename,
                    storage_path=storage_path,
                    file_size=len(file_content),
                    error=classification.get("error", False),
                    # 新增字段
                    detected_investor=classification.get("detected_investor", ""),
                    detected_project=classification.get("detected_project", ""),
                    investor_match=classification.get("investor_match", None),
                    project_match=classification.get("project_match", None),
                    # 解析内容相关字段
                    parsed_content_path=temp_parsed_content_path,
                    parsed_content_preview=parsed_content[:500] + "..." if len(parsed_content) > 500 else parsed_content
                )

                # 将临时文件路径添加到结果中
                result_dict = result.dict()
                result_dict['temp_file_path'] = temp_file_path

                classification_results.append(result_dict)
                processed_count += 1

                # 9. 更新任务进度（内存缓存）
                if task_id in task_cache:
                    task_cache[task_id]["processed_files"] = processed_count

                logger.info(f"文件处理完成 {processed_count}/{len(files)}: {file.filename} -> {classification.get('output_name', 'Unknown')}")

            except Exception as e:
                logger.error(f"文件处理失败 {file.filename}: {str(e)}")
                result = FileClassificationResult(
                    filename=file.filename,
                    original_filename=file.filename,
                    main_process="未分类",
                    sub_process="未分类",
                    output_name="处理失败",
                    confidence=0.0,
                    reason=f"处理失败: {str(e)}",
                    suggested_filename=file.filename,
                    storage_path="",
                    file_size=len(file_content) if 'file_content' in locals() else 0,
                    error=True,
                    detected_investor="",
                    detected_project="",
                    investor_match=None,
                    project_match=None
                )
                classification_results.append(result.dict())
                processed_count += 1

                # 更新进度（内存缓存）
                if task_id in task_cache:
                    task_cache[task_id]["processed_files"] = processed_count

        # 更新任务状态为完成（内存缓存）
        if task_id in task_cache:
            from datetime import datetime
            task_cache[task_id]["status"] = "completed"
            task_cache[task_id]["completed_at"] = datetime.now().isoformat()

        # 将分类结果保存到临时存储
        temp_dir = "temp_classifications"
        os.makedirs(temp_dir, exist_ok=True)
        with open(os.path.join(temp_dir, f"{task_id}.json"), 'w', encoding='utf-8') as f:
            json.dump(classification_results, f, ensure_ascii=False, indent=2)

        logger.info(f"分类任务完成: {task_id}, 成功处理 {processed_count} 个文件")

    except Exception as e:
        logger.error(f"分类任务失败: {task_id} - {str(e)}")
        # 更新任务状态为失败（内存缓存）
        if task_id in task_cache:
            task_cache[task_id]["status"] = "failed"
            task_cache[task_id]["error_message"] = str(e)

async def process_files_classification_with_parsed_content(
    task_id: str,
    project_code: str,
    project_name: str,
    parsed_files: List[Dict],
    current_user: dict
):
    """使用AI助手解析后的内容进行文件分类"""
    try:
        logger.info(f"开始处理分类任务: {task_id}, 项目: {project_code}, 文件数: {len(parsed_files)}")

        # 获取档案清单
        conn = get_db_connection()
        with conn.cursor() as cursor:
            cursor.execute("SELECT * FROM project_archive_checklist ORDER BY main_process, sub_process")
            checklist = cursor.fetchall()
        conn.close()

        classification_results = []
        processed_count = 0

        # 使用AI助手的chat_with_files功能进行批量分类
        from app.api.endpoints.ai_assistant import ai_chat_service

        # 准备文件内容用于AI分类
        valid_files = [f for f in parsed_files if f.get("success", False) and f.get("content")]

        if valid_files:
            # 构建分类提示词
            checklist_text = ""
            for i, item in enumerate(checklist, 1):
                checklist_text += f"{i}. {item['main_process']} -> {item['sub_process']} -> {item['output_name']}\n"
                checklist_text += f"   描述: {item['content_description']}\n"
                if item.get('keywords'):
                    checklist_text += f"   关键词: {item['keywords']}\n"
                checklist_text += "\n"

            classification_query = f"""你是一个专业的项目档案分类专家。请根据以下项目管理档案清单，对上传的文件进行精确分类。

档案清单：
{checklist_text}

请对每个文件进行分类，并严格按照以下JSON格式返回结果：
{{
    "classifications": [
        {{
            "filename": "文件名",
            "main_process": "项目管理主流程",
            "sub_process": "项目管理子流程",
            "output_name": "项目管理输出物",
            "confidence": 0.85,
            "reason": "基于文件名和内容分析的详细理由"
        }}
    ]
}}

分析要求：
1. 仔细分析文件名和内容，识别关键信息
2. 匹配最符合的档案类别
3. 给出分类置信度（0-1之间的小数）
4. 说明分类理由"""

            # 调用AI进行批量分类
            try:
                result = await ai_chat_service.chat_with_files(classification_query, valid_files)

                if result.get("success"):
                    ai_response = result.get("answer", "")
                    # 解析AI返回的分类结果
                    classifications = parse_batch_classification_result(ai_response, valid_files)
                else:
                    logger.error(f"AI分类失败: {result.get('error', '未知错误')}")
                    classifications = create_fallback_classifications(valid_files)

            except Exception as e:
                logger.error(f"AI批量分类失败: {str(e)}")
                classifications = create_fallback_classifications(valid_files)
        else:
            classifications = []

        # 处理所有文件（包括解析失败的）
        for parsed_file in parsed_files:
            try:
                filename = parsed_file["filename"]

                # 查找对应的分类结果
                classification = None
                for cls in classifications:
                    if cls.get("filename") == filename:
                        classification = cls
                        break

                # 如果没有找到分类结果，使用默认值
                if not classification:
                    if parsed_file.get("success", False):
                        classification = {
                            "main_process": "未分类",
                            "sub_process": "未分类",
                            "output_name": "未分类文档",
                            "confidence": 0.0,
                            "reason": "AI分类失败，使用默认分类"
                        }
                    else:
                        classification = {
                            "main_process": "未分类",
                            "sub_process": "未分类",
                            "output_name": "解析失败",
                            "confidence": 0.0,
                            "reason": parsed_file.get("error", "文件解析失败")
                        }

                # 保存临时文件（如果有文件内容）
                temp_file_path = ""
                if parsed_file.get("file_content"):
                    temp_file_path = file_storage.save_temp_file(
                        task_id, filename, parsed_file["file_content"]
                    )

                # 生成建议文件名和存储路径
                suggested_filename = generate_suggested_filename(filename, classification)
                storage_path = generate_storage_path(project_code, classification)

                # 构建结果
                result = FileClassificationResult(
                    filename=filename,
                    original_filename=filename,
                    main_process=classification.get("main_process", "未分类"),
                    sub_process=classification.get("sub_process", "未分类"),
                    output_name=classification.get("output_name", "未分类文档"),
                    confidence=classification.get("confidence", 0.0),
                    reason=classification.get("reason", ""),
                    suggested_filename=suggested_filename,
                    storage_path=storage_path,
                    file_size=parsed_file.get("size", 0),
                    error=not parsed_file.get("success", False)
                )

                # 将临时文件路径添加到结果中
                result_dict = result.dict()
                result_dict["temp_file_path"] = temp_file_path
                classification_results.append(result_dict)

                processed_count += 1
                logger.info(f"文件分类完成: {filename} -> {classification.get('output_name', 'Unknown')}")

            except Exception as e:
                logger.error(f"处理文件失败 {parsed_file.get('filename', 'Unknown')}: {str(e)}")
                # 添加错误结果
                error_result = FileClassificationResult(
                    filename=parsed_file.get("filename", "Unknown"),
                    original_filename=parsed_file.get("filename", "Unknown"),
                    main_process="未分类",
                    sub_process="未分类",
                    output_name="处理失败",
                    confidence=0.0,
                    reason=f"处理失败: {str(e)}",
                    suggested_filename=parsed_file.get("filename", "Unknown"),
                    storage_path="",
                    file_size=parsed_file.get("size", 0),
                    error=True
                )
                classification_results.append(error_result.dict())
                processed_count += 1

        # 更新任务状态为完成
        conn = get_db_connection()
        with conn.cursor() as cursor:
            cursor.execute("""
                UPDATE file_classification_tasks
                SET status = 'completed', processed_files = %s, completed_at = NOW()
                WHERE task_id = %s
            """, (processed_count, task_id))
        conn.commit()
        conn.close()

        # 保存分类结果到缓存
        import json
        results_cache[task_id] = {
            "status": "completed",
            "results": classification_results,
            "total_files": len(parsed_files),
            "processed_files": processed_count
        }

        logger.info(f"分类任务完成: {task_id}, 处理文件数: {processed_count}")

    except Exception as e:
        logger.error(f"分类任务失败: {task_id}, 错误: {str(e)}")

        # 更新任务状态为失败
        try:
            conn = get_db_connection()
            with conn.cursor() as cursor:
                cursor.execute("""
                    UPDATE file_classification_tasks
                    SET status = 'failed', error_message = %s
                    WHERE task_id = %s
                """, (str(e), task_id))
            conn.commit()
            conn.close()
        except:
            pass

        # 保存错误结果到缓存
        results_cache[task_id] = {
            "status": "failed",
            "error": str(e),
            "results": [],
            "total_files": len(parsed_files) if 'parsed_files' in locals() else 0,
            "processed_files": 0
        }

async def process_files_classification(
    task_id: str,
    project_code: str,
    project_name: str,
    files: List[UploadFile],
    current_user: dict
):
    """后台处理文件分类任务（旧版本，保留兼容性）"""
    try:
        logger.info(f"开始处理分类任务: {task_id}, 项目: {project_code}, 文件数: {len(files)}")
        
        # 获取档案清单（使用预定义清单）
        checklist = DEFAULT_ARCHIVE_CHECKLIST

        # 设置期望的项目信息（用于AI匹配）
        expected_project_name = project_name
        expected_investment_entity = "财险"  # 默认值，可以从项目信息中获取

        classification_results = []
        processed_count = 0
        
        for file in files:
            try:
                # 读取文件内容
                file_content = await file.read()

                # 验证文件大小（限制为20MB）
                if len(file_content) > 20 * 1024 * 1024:
                    result = FileClassificationResult(
                        filename=file.filename,
                        original_filename=file.filename,
                        main_process="未分类",
                        sub_process="未分类",
                        output_name="文件过大",
                        confidence=0.0,
                        reason="文件大小超过20MB限制",
                        suggested_filename=file.filename,
                        storage_path="",
                        file_size=len(file_content),
                        error=True,
                        detected_investor="",
                        detected_project="",
                        investor_match=None,
                        project_match=None
                    )
                    classification_results.append(result.dict())
                    processed_count += 1
                    continue

                # 保存临时文件
                temp_file_path = file_storage.save_temp_file(task_id, file.filename, file_content)
                
                # 使用AI进行分类
                classifier = get_archive_classifier()
                classification = await classifier.classify_document(
                    file_content, file.filename, file.content_type, checklist,
                    expected_project_name, expected_investment_entity
                )



                # 生成建议文件名和存储路径
                suggested_filename = classifier.get_suggested_filename(file.filename, classification)
                storage_path = classifier.get_storage_path(project_code, classification)
                
                # 构建结果
                result = FileClassificationResult(
                    filename=file.filename,
                    original_filename=file.filename,
                    main_process=classification.get("main_process", "未分类"),
                    sub_process=classification.get("sub_process", "未分类"),
                    output_name=classification.get("output_name", "未分类文档"),
                    confidence=classification.get("confidence", 0.0),
                    reason=classification.get("reason", ""),
                    suggested_filename=suggested_filename,
                    storage_path=storage_path,
                    file_size=len(file_content),
                    error=classification.get("error", False),
                    # 新增字段
                    detected_investor=classification.get("detected_investor", ""),
                    detected_project=classification.get("detected_project", ""),
                    investor_match=classification.get("investor_match", None),
                    project_match=classification.get("project_match", None)
                )

                # 将临时文件路径添加到结果中（用于后续确认时移动文件）
                result_dict = result.dict()
                result_dict['temp_file_path'] = temp_file_path
                
                classification_results.append(result_dict)
                processed_count += 1
                
                # 更新任务进度
                conn = get_db_connection()
                with conn.cursor() as cursor:
                    cursor.execute("""
                        UPDATE file_classification_tasks 
                        SET processed_files = %s 
                        WHERE task_id = %s
                    """, (processed_count, task_id))
                conn.commit()
                conn.close()
                
                logger.info(f"文件分类完成: {file.filename} -> {classification.get('output_name', 'Unknown')}")
                
            except Exception as e:
                logger.error(f"文件分类失败 {file.filename}: {str(e)}")
                result = FileClassificationResult(
                    filename=file.filename,
                    original_filename=file.filename,
                    main_process="未分类",
                    sub_process="未分类",
                    output_name="分类失败",
                    confidence=0.0,
                    reason=f"分类失败: {str(e)}",
                    suggested_filename=file.filename,
                    storage_path="",
                    file_size=len(file_content) if 'file_content' in locals() else 0,
                    error=True,
                    detected_investor="",
                    detected_project="",
                    investor_match=None,
                    project_match=None
                )
                classification_results.append(result.dict())
                processed_count += 1
        
        # 更新任务状态为完成
        conn = get_db_connection()
        with conn.cursor() as cursor:
            cursor.execute("""
                UPDATE file_classification_tasks 
                SET status = 'completed', processed_files = %s 
                WHERE task_id = %s
            """, (processed_count, task_id))
        conn.commit()
        conn.close()
        
        # 将分类结果保存到临时存储（可以用Redis或文件）
        # 这里简单保存到文件
        temp_dir = "temp_classifications"
        os.makedirs(temp_dir, exist_ok=True)
        with open(os.path.join(temp_dir, f"{task_id}.json"), 'w', encoding='utf-8') as f:
            json.dump(classification_results, f, ensure_ascii=False, indent=2)
        
        logger.info(f"分类任务完成: {task_id}, 处理文件数: {processed_count}")
        
    except Exception as e:
        logger.error(f"分类任务失败 {task_id}: {str(e)}")
        # 更新任务状态为失败
        try:
            conn = get_db_connection()
            with conn.cursor() as cursor:
                cursor.execute("""
                    UPDATE file_classification_tasks 
                    SET status = 'failed', error_message = %s 
                    WHERE task_id = %s
                """, (str(e), task_id))
            conn.commit()
            conn.close()
        except:
            pass

@router.get("/tasks/{task_id}/status")
async def get_task_status(task_id: str, current_user: dict = Depends(get_current_user)):
    """获取分类任务状态"""
    try:
        logger.info(f"查询任务状态: {task_id}")

        # 从内存缓存中获取任务状态
        if task_id not in task_cache:
            logger.warning(f"任务不存在: {task_id}")
            raise HTTPException(status_code=404, detail="任务不存在")

        task = task_cache[task_id]
        logger.info(f"任务状态查询成功: {task_id} -> {task.get('status', 'unknown')}")
        return {
            "success": True,
            "data": task
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取任务状态失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取任务状态失败: {str(e)}")

@router.get("/tasks/{task_id}/results")
async def get_classification_results(task_id: str, current_user: dict = Depends(get_current_user)):
    """获取分类结果"""
    try:
        # 检查任务是否存在（内存缓存）
        if task_id not in task_cache:
            raise HTTPException(status_code=404, detail="任务不存在")

        task = task_cache[task_id]
        if task['status'] != 'completed':
            return {
                "success": False,
                "message": f"任务状态: {task['status']}，结果尚未准备好"
            }
        
        # 读取分类结果
        temp_file = os.path.join("temp_classifications", f"{task_id}.json")
        if not os.path.exists(temp_file):
            raise HTTPException(status_code=404, detail="分类结果不存在")
        
        with open(temp_file, 'r', encoding='utf-8') as f:
            results = json.load(f)
        
        return {
            "success": True,
            "data": results,
            "total": len(results)
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取分类结果失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取分类结果失败: {str(e)}")

@router.post("/tasks/{task_id}/confirm")
async def confirm_classifications(
    task_id: str,
    http_request: Request,
    current_user: dict = None
):
    """确认分类结果并保存文件"""
    try:
        # 直接读取和解析请求体
        body = await http_request.body()
        logger.info(f"原始请求体: {body.decode('utf-8')}")

        import json
        request_data = json.loads(body.decode('utf-8'))
        logger.info(f"解析后的请求数据: {request_data}")

        # 验证必要字段
        if 'confirmations' not in request_data:
            raise HTTPException(status_code=400, detail="缺少confirmations字段")

        confirmations = request_data['confirmations']
        logger.info(f"收到确认分类请求: task_id={task_id}")
        logger.info(f"confirmations数量: {len(confirmations)}")
        for i, conf in enumerate(confirmations):
            logger.info(f"确认项 {i}: {conf}")
        # 验证任务（内存缓存）
        if task_id not in task_cache:
            raise HTTPException(status_code=404, detail="任务不存在")

        task = task_cache[task_id]
        if task['status'] != 'completed':
            raise HTTPException(status_code=400, detail="任务尚未完成")

        project_code = task['project_code']

        # 读取原始分类结果
        temp_file = os.path.join("temp_classifications", f"{task_id}.json")
        if not os.path.exists(temp_file):
            raise HTTPException(status_code=404, detail="分类结果不存在")

        with open(temp_file, 'r', encoding='utf-8') as f:
            original_results = json.load(f)

        # 读取原始文件（需要重新上传或从临时存储读取）
        # 这里假设文件已经临时保存
        temp_files_dir = os.path.join("temp_files", task_id)

        saved_files = []
        failed_files = []

        for confirmation_data in confirmations:
            try:
                if not confirmation_data.get('confirmed', True):
                    continue

                # 查找对应的原始结果
                original_result = None
                for result in original_results:
                    if result['filename'] == confirmation_data['filename']:
                        original_result = result
                        break

                if not original_result:
                    failed_files.append({
                        "filename": confirmation_data['filename'],
                        "error": "未找到原始分类结果"
                    })
                    continue

                # 使用用户确认的分类信息
                final_classification = {
                    "main_process": confirmation_data['main_process'],
                    "sub_process": confirmation_data['sub_process'],
                    "output_name": confirmation_data['output_name']
                }

                # 生成最终的文件名和存储路径
                classifier = get_archive_classifier()
                suggested_filename = classifier.get_suggested_filename(
                    confirmation_data['filename'], final_classification
                )
                storage_path = classifier.get_storage_path(
                    project_code, final_classification
                )

                # 从临时文件移动到最终位置
                temp_file_path = original_result.get('temp_file_path')
                if not temp_file_path or not os.path.exists(temp_file_path):
                    failed_files.append({
                        "filename": confirmation_data['filename'],
                        "error": "临时文件不存在"
                    })
                    continue

                # 获取临时解析内容路径
                temp_parsed_content_path = original_result.get('parsed_content_path', '')

                # 移动文件和解析内容到档案目录
                if temp_parsed_content_path and os.path.exists(temp_parsed_content_path):
                    # 同时移动原始文件和解析内容
                    final_file_path, final_parsed_path = file_storage.move_to_archive_with_content(
                        temp_file_path, temp_parsed_content_path, project_code, final_classification, suggested_filename
                    )
                    logger.info(f"文件和解析内容已保存: {final_file_path}, {final_parsed_path}")
                else:
                    # 只移动原始文件
                    final_file_path = file_storage.move_to_archive(
                        temp_file_path, project_code, final_classification, suggested_filename
                    )
                    logger.info(f"仅保存原始文件: {final_file_path}")

                # 保存文件记录到数据库
                conn = get_db_connection()
                with conn.cursor() as cursor:
                    cursor.execute("""
                        INSERT INTO project_archive_files
                        (project_code, project_name, original_filename, stored_filename,
                         file_path, file_size, file_type, main_process, sub_process,
                         output_name, classification_confidence, classification_reason,
                         is_confirmed, uploaded_by, confirmed_by, confirmed_at)
                        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                    """, (
                        project_code,
                        "项目名称",  # 需要从project_account_book获取
                        confirmation_data['filename'],
                        suggested_filename,
                        final_file_path,
                        original_result.get('file_size', 0),
                        os.path.splitext(confirmation_data['filename'])[1],
                        confirmation_data['main_process'],
                        confirmation_data['sub_process'],
                        confirmation_data['output_name'],
                        original_result.get('confidence', 0.0),
                        original_result.get('reason', ''),
                        1,  # is_confirmed
                        original_result.get('uploaded_by', current_user.get('username', 'unknown') if current_user else 'unknown'),
                        current_user.get('username', 'unknown') if current_user else 'unknown',
                        datetime.now()
                    ))
                conn.commit()
                conn.close()

                saved_files.append({
                    "filename": confirmation_data['filename'],
                    "stored_filename": suggested_filename,
                    "storage_path": final_file_path,
                    "classification": final_classification
                })

            except Exception as e:
                logger.error(f"保存文件失败 {confirmation_data['filename']}: {str(e)}")
                failed_files.append({
                    "filename": confirmation_data['filename'],
                    "error": str(e)
                })

        # 清理临时文件
        try:
            if os.path.exists(temp_file):
                os.remove(temp_file)
            if os.path.exists(temp_files_dir):
                shutil.rmtree(temp_files_dir)
        except:
            pass

        # 移除自动更新项目进度功能
        # 项目进度应该由项目管理人员手动更新，而不是根据文件数量自动推测
        if len(saved_files) > 0:
            logger.info(f"项目 {project_code} 成功保存 {len(saved_files)} 个文件")

        return {
            "success": True,
            "message": f"成功保存 {len(saved_files)} 个文件",
            "saved_files": saved_files,
            "failed_files": failed_files,
            "total_confirmed": len(confirmations),
            "total_saved": len(saved_files),
            "total_failed": len(failed_files)
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"确认分类失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"确认分类失败: {str(e)}")

@router.get("/projects/{project_code}/files")
async def get_project_files(
    project_code: str,
    main_process: Optional[str] = None,
    sub_process: Optional[str] = None,
    current_user: dict = Depends(get_current_user)
):
    """获取项目的档案文件列表"""
    try:
        conn = get_db_connection()
        with conn.cursor() as cursor:
            # 构建查询条件
            where_conditions = ["project_code = %s"]
            params = [project_code]

            if main_process:
                where_conditions.append("main_process = %s")
                params.append(main_process)

            if sub_process:
                where_conditions.append("sub_process = %s")
                params.append(sub_process)

            where_clause = " AND ".join(where_conditions)

            cursor.execute(f"""
                SELECT * FROM project_archive_files
                WHERE {where_clause}
                ORDER BY main_process, sub_process, output_name, created_at DESC
            """, params)
            files = cursor.fetchall()

        conn.close()

        return {
            "success": True,
            "data": files,
            "total": len(files)
        }

    except Exception as e:
        logger.error(f"获取项目文件列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取项目文件列表失败: {str(e)}")

@router.delete("/files/{file_id}")
async def delete_archive_file(
    file_id: int,
    current_user: dict = Depends(get_current_user)
):
    """删除档案文件"""
    try:
        conn = get_db_connection()
        with conn.cursor() as cursor:
            # 获取文件信息
            cursor.execute("SELECT * FROM project_archive_files WHERE id = %s", (file_id,))
            file_info = cursor.fetchone()

            if not file_info:
                raise HTTPException(status_code=404, detail="文件不存在")

            # 删除物理文件
            if os.path.exists(file_info['file_path']):
                os.remove(file_info['file_path'])

            # 删除数据库记录
            cursor.execute("DELETE FROM project_archive_files WHERE id = %s", (file_id,))

        conn.commit()
        conn.close()

        return {
            "success": True,
            "message": "文件删除成功"
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除文件失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"删除文件失败: {str(e)}")
