# PMO系统全平台架构设计

## 一、整体架构

```
+-------------------+     +-------------------+     +-------------------+
|                   |     |                   |     |                   |
|  Web应用          |     |  桌面客户端       |     |  移动应用         |
|  (pmo-web)        |     |  (Electron)       |     |  (Flutter)        |
|                   |     |                   |     |                   |
+-------------------+     +-------------------+     +-------------------+
           |                       |                         |
           |                       |                         |
           v                       v                         v
+-----------------------------------------------------------------------+
|                                                                       |
|                          统一API层 (pmo-backend)                      |
|                                                                       |
+-----------------------------------------------------------------------+
                                  |
                                  |
                                  v
+-----------------------------------------------------------------------+
|                                                                       |
|                             MySQL数据库                               |
|                                                                       |
+-----------------------------------------------------------------------+
```

## 二、后端架构 (pmo-backend)

### 1. 目录结构（直接迁移云函数）

```
pmo-backend/
├── .env                     # 环境变量配置
├── main.py                  # 应用入口
├── requirements.txt         # 依赖管理
├── app/                     # 应用核心
│   ├── __init__.py          # 初始化文件
│   ├── api/                 # API路由
│   │   ├── __init__.py      # API路由注册
│   │   ├── auth.py          # 认证API
│   │   ├── projects.py      # 项目API
│   │   ├── users.py         # 用户API
│   │   ├── dashboard.py     # 仪表盘API (红黑榜)
│   │   ├── timesheet.py     # 工时API
│   │   ├── reports.py       # 报告API
│   │   ├── options.py       # 选项API
│   │   └── teams.py         # 团队API
│   ├── core/                # 核心功能
│   │   ├── __init__.py      # 初始化文件
│   │   ├── config.py        # 配置管理
│   │   ├── security.py      # 安全相关
│   │   └── errors.py        # 错误处理
│   ├── db/                  # 数据库
│   │   ├── __init__.py      # 初始化文件
│   │   ├── connection.py    # 数据库连接
│   │   └── utils.py         # 数据库工具函数
│   ├── cloud_functions/     # 直接迁移的云函数代码
│   │   ├── __init__.py      # 初始化文件
│   │   ├── get_project_list/        # 原getProjectList
│   │   │   └── index.py             # 原云函数代码
│   │   ├── get_project_details/     # 原getProjectDetails
│   │   │   └── index.py             # 原云函数代码
│   │   ├── get_red_black_board/     # 原getRedBlackBoard
│   │   │   └── index.py             # 原云函数代码
│   │   ├── update_project_details/  # 原updateProjectDetails
│   │   │   └── index.py             # 原云函数代码
│   │   ├── update_project_account_book/ # 原updateProjectAccountBook
│   │   │   └── index.py             # 原云函数代码
│   │   ├── get_project_change_history/ # 原getProjectChangeHistory
│   │   │   └── index.py             # 原云函数代码
│   │   ├── get_database_options/    # 原getDatabaseOptions
│   │   │   └── index.py             # 原云函数代码
│   │   ├── get_user/                # 原getuser
│   │   │   └── index.py             # 原云函数代码
│   │   ├── project_hours_management/ # 原project_hours_management
│   │   │   └── index.py             # 原云函数代码
│   │   ├── weekly_report_functions/ # 原weekly_report_functions
│   │   │   └── index.py             # 原云函数代码
│   │   ├── user_project_management/ # 原user_project_management_py
│   │   │   └── index.py             # 原云函数代码
│   │   └── itbp_team_management/    # 原itbpTeamManagement
│   │       └── index.py             # 原云函数代码
│   └── utils/               # 工具函数
│       ├── __init__.py      # 初始化文件
│       ├── date_utils.py    # 日期工具
│       ├── response_utils.py # 响应工具
│       └── validation_utils.py # 验证工具
└── tests/                   # 测试
    ├── __init__.py          # 初始化文件
    ├── conftest.py          # 测试配置
    ├── test_api/            # API测试
    └── test_cloud_functions/ # 云函数测试
```

### 2. 云函数直接迁移策略

#### 2.1 迁移方法

1. **直接复制代码**：将云函数代码直接复制到对应的目录结构中
2. **最小修改适配**：只修改必要的部分，如环境变量引用、入口函数等
3. **保留原有逻辑**：完整保留业务逻辑，不做任何算法或流程上的改变
4. **统一接口封装**：在API路由层对云函数进行封装，提供统一的RESTful接口

#### 2.2 适配修改内容

1. **入口函数适配**：将云函数的入口函数（如main_handler）适配为普通函数
   ```python
   # 原云函数代码
   def main_handler(event, context):
       # 处理逻辑
       return response
   
   # 适配后的代码
   def main_handler(event_data):
       # 处理逻辑
       return response
   ```

2. **环境变量适配**：将云函数环境变量适配为FastAPI配置
   ```python
   # 原云函数代码
   DB_CONFIG = {
       'host': os.environ['DB_HOST'],
       'port': int(os.environ['DB_PORT']),
       # ...
   }
   
   # 适配后的代码
   from app.core.config import settings
   
   DB_CONFIG = {
       'host': settings.DB_HOST,
       'port': settings.DB_PORT,
       # ...
   }
   ```

3. **请求参数适配**：将云函数的事件对象适配为FastAPI的请求参数
   ```python
   # API路由层适配
   @router.get("/projects")
   def get_projects(
       entity: str = Query(None),
       category: str = Query(None),
       current_user = Depends(get_current_user)
   ):
       # 构造云函数需要的event对象
       event = {
           "queryStringParameters": {
               "entity": entity,
               "category": category
           }
       }
       # 调用云函数
       result = get_project_list_handler(event)
       return result
   ```

4. **响应格式适配**：将云函数的响应格式适配为FastAPI的响应格式
   ```python
   # 原云函数响应
   return {
       "statusCode": 200,
       "body": json.dumps({
           "code": 200,
           "message": "success",
           "data": data
       })
   }
   
   # 适配后的响应
   from app.utils.response_utils import standard_response
   
   return standard_response(data=data)
   ```

5. **日志系统适配**：将云函数的日志系统适配为FastAPI的日志系统
   ```python
   # 原云函数日志
   logger = logging.getLogger()
   logger.setLevel(logging.INFO)
   
   # 适配后的日志
   from app.core.logger import get_logger
   
   logger = get_logger(__name__)
   ```

### 3. API路由映射

| 原云函数 | 新API路由 | HTTP方法 | 描述 |
|---------|----------|---------|------|
| getProjectList | /api/projects | GET | 获取项目列表 |
| getProjectDetails | /api/projects/{project_code} | GET | 获取项目详情 |
| getRedBlackBoard | /api/dashboard/redblack | GET | 获取红黑榜 |
| updateProjectDetails | /api/projects/{project_code} | PUT | 更新项目详情 |
| updateProjectAccountBook | /api/projects/{project_code}/accountbook | PUT | 更新项目账本 |
| getProjectChangeHistory | /api/projects/{project_code}/history | GET | 获取项目变更历史 |
| getDatabaseOptions | /api/options | GET | 获取数据库选项 |
| getuser | /api/users | GET | 获取用户信息 |
| project_hours_management | /api/timesheet | GET/POST | 项目工时管理 |
| weekly_report_functions | /api/reports/weekly | GET/POST | 周报功能 |
| user_project_management_py | /api/users/projects | GET/POST | 用户项目管理 |
| itbpTeamManagement | /api/teams | GET/POST | 团队管理 |

### 4. API路由层实现示例

```python
# app/api/projects.py
from fastapi import APIRouter, Query, Depends, HTTPException, Path
from app.cloud_functions.get_project_list.index import main_handler as get_project_list_handler
from app.cloud_functions.get_project_details.index import main_handler as get_project_details_handler
from app.core.security import get_current_user
import json

router = APIRouter()

@router.get("/")
def get_projects(
    entity: str = Query(None, description="投资主体"),
    category: str = Query(None, description="类别(implementation/delayed)"),
    current_user = Depends(get_current_user)
):
    """
    获取项目列表
    """
    try:
        # 构造云函数需要的event对象
        event = {
            "queryStringParameters": {
                "entity": entity,
                "category": category
            }
        }
        # 调用云函数
        result = get_project_list_handler(event, None)
        # 解析云函数响应
        if result.get("statusCode") == 200:
            body = json.loads(result.get("body", "{}"))
            return body
        else:
            raise HTTPException(status_code=result.get("statusCode", 500), detail="获取项目列表失败")
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/{project_code}")
def get_project_details(
    project_code: str = Path(..., description="项目编号"),
    current_user = Depends(get_current_user)
):
    """
    获取项目详情
    """
    try:
        # 构造云函数需要的event对象
        event = {
            "pathParameters": {
                "project_code": project_code
            }
        }
        # 调用云函数
        result = get_project_details_handler(event, None)
        # 解析云函数响应
        if result.get("statusCode") == 200:
            body = json.loads(result.get("body", "{}"))
            return body
        else:
            raise HTTPException(status_code=result.get("statusCode", 500), detail="获取项目详情失败")
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
```

### 5. 配置管理

为了支持云函数代码的直接迁移，需要在配置管理中添加云函数所需的所有环境变量：

```python
# app/core/config.py
import os
from pydantic import BaseSettings, Field
from dotenv import load_dotenv

# 加载.env文件
load_dotenv()

class Settings(BaseSettings):
    """应用配置"""
    # 应用设置
    APP_NAME: str = "PMO系统"
    API_PREFIX: str = "/api"
    DEBUG: bool = Field(default=False)
    
    # 数据库设置 - 与云函数保持一致的环境变量名
    DB_HOST: str = Field(default=os.getenv("DB_HOST", "localhost"))
    DB_PORT: int = Field(default=int(os.getenv("DB_PORT", "3306")))
    DB_USER: str = Field(default=os.getenv("DB_USER", "root"))
    DB_PASSWORD: str = Field(default=os.getenv("DB_PASSWORD", ""))
    DB_NAME: str = Field(default=os.getenv("DB_NAME", "pmo"))
    
    # 安全设置
    SECRET_KEY: str = Field(default=os.getenv("SECRET_KEY", ""))
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 60 * 24 * 7  # 7天
    
    # 其他云函数可能使用的环境变量
    # ...添加云函数中用到的其他环境变量
    
    class Config:
        env_file = ".env"
        case_sensitive = True

settings = Settings()
```

### 6. 数据库连接适配

为了让云函数代码能够使用统一的数据库连接管理，需要适配数据库连接：

```python
# app/db/connection.py
import pymysql
from pymysql.cursors import DictCursor
from contextlib import contextmanager
from app.core.config import settings
import logging

logger = logging.getLogger(__name__)

# 与云函数保持一致的数据库配置
DB_CONFIG = {
    'host': settings.DB_HOST,
    'port': settings.DB_PORT,
    'user': settings.DB_USER,
    'password': settings.DB_PASSWORD,
    'database': settings.DB_NAME,
    'charset': 'utf8mb4',
    'cursorclass': DictCursor
}

def get_db_connection():
    """获取数据库连接 - 与云函数保持一致的接口"""
    try:
        return pymysql.connect(**DB_CONFIG)
    except Exception as e:
        logger.error(f"数据库连接错误: {str(e)}")
        raise e

@contextmanager
def db_transaction():
    """数据库事务上下文管理器"""
    conn = None
    try:
        conn = get_db_connection()
        yield conn
        conn.commit()
    except Exception as e:
        if conn:
            conn.rollback()
        raise e
    finally:
        if conn:
            conn.close()
```

### 7. 应用入口

应用入口文件负责初始化FastAPI应用，注册路由和中间件：

```python
# main.py
import uvicorn
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from app.api import api_router
from app.core.config import settings

app = FastAPI(
    title=settings.APP_NAME,
    openapi_url=f"{settings.API_PREFIX}/openapi.json",
    docs_url=f"{settings.API_PREFIX}/docs",
    redoc_url=f"{settings.API_PREFIX}/redoc",
)

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 在生产环境中应该限制来源
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 注册API路由
app.include_router(api_router, prefix=settings.API_PREFIX)

if __name__ == "__main__":
    uvicorn.run("main:app", host="0.0.0.0", port=8000, reload=True)
```

## 三、前端架构 (pmo-web)

### 1. 目录结构

```
pmo-web/
├── node_modules/           # 依赖包
├── public/                 # 静态资源
│   ├── favicon.ico         # 网站图标
│   └── index.html          # HTML模板
├── src/                    # 源代码
│   ├── api/                # API请求
│   │   ├── auth.js         # 认证API
│   │   ├── projects.js     # 项目API
│   │   ├── users.js        # 用户API
│   │   ├── dashboard.js    # 仪表盘API
│   │   ├── timesheet.js    # 工时API
│   │   ├── reports.js      # 报告API
│   │   └── teams.js        # 团队API
│   ├── assets/             # 资源文件
│   │   ├── images/         # 图片
│   │   ├── styles/         # 样式
│   │   └── fonts/          # 字体
│   ├── components/         # 公共组件
│   │   ├── common/         # 通用组件
│   │   ├── layout/         # 布局组件
│   │   └── business/       # 业务组件
│   ├── router/             # 路由配置
│   │   ├── index.js        # 路由入口
│   │   └── routes.js       # 路由定义
│   ├── store/              # 状态管理
│   │   ├── index.js        # 状态入口
│   │   ├── modules/        # 状态模块
│   │   └── persistence.js  # 状态持久化
│   ├── utils/              # 工具函数
│   │   ├── request.js      # 请求工具
│   │   ├── auth.js         # 认证工具
│   │   ├── date.js         # 日期工具
│   │   └── validation.js   # 验证工具
│   ├── views/              # 页面组件
│   │   ├── auth/           # 认证页面
│   │   ├── dashboard/      # 仪表盘页面
│   │   │   ├── RedBlackBoard.vue      # 红黑榜页面
│   │   │   ├── InvestmentDetails.vue  # 投资详情页面
│   │   │   └── components/            # 仪表盘组件
│   │   ├── projects/       # 项目页面
│   │   ├── timesheet/      # 工时页面
│   │   ├── reports/        # 报告页面
│   │   └── settings/       # 设置页面
│   ├── App.vue             # 根组件
│   └── main.js             # 入口文件
├── .env                    # 环境变量
├── .env.development        # 开发环境变量
├── .env.production         # 生产环境变量
├── vite.config.js          # Vite配置
├── package.json            # 项目配置
└── README.md               # 项目说明
```

### 2. 前端与云函数适配

为了与后端直接迁移的云函数代码无缝对接，前端需要做一些适配工作。主要是在API请求层面进行调整，使其符合后端API的请求和响应格式。

#### 2.1 API请求适配

```javascript
// src/api/projects.js
import request from '@/utils/request';

// 获取项目列表
export function getProjectList(params) {
  return request({
    url: '/api/projects',
    method: 'get',
    params
  });
}

// 获取项目详情
export function getProjectDetails(projectCode) {
  return request({
    url: `/api/projects/${projectCode}`,
    method: 'get'
  });
}

// 更新项目详情
export function updateProjectDetails(projectCode, data) {
  return request({
    url: `/api/projects/${projectCode}`,
    method: 'put',
    data
  });
}

// 更新项目账本
export function updateProjectAccountBook(projectCode, data) {
  return request({
    url: `/api/projects/${projectCode}/accountbook`,
    method: 'put',
    data
  });
}

// 获取项目变更历史
export function getProjectChangeHistory(projectCode) {
  return request({
    url: `/api/projects/${projectCode}/history`,
    method: 'get'
  });
}
```

#### 2.2 请求工具适配

```javascript
// src/utils/request.js
import axios from 'axios';
import { useUserStore } from '@/store/modules/user';
import { ElMessage } from 'element-plus';

// 创建axios实例
const service = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || '',
  timeout: 15000
});

// 请求拦截器
service.interceptors.request.use(
  (config) => {
    const userStore = useUserStore();
    if (userStore.token) {
      config.headers['Authorization'] = `Bearer ${userStore.token}`;
    }
    return config;
  },
  (error) => {
    console.error('请求错误:', error);
    return Promise.reject(error);
  }
);

// 响应拦截器
service.interceptors.response.use(
  (response) => {
    const res = response.data;
    
    // 适配云函数响应格式
    // 云函数返回的格式为 { code: 200, message: 'success', data: {...} }
    if (res.code !== 200) {
      ElMessage({
        message: res.message || '请求失败',
        type: 'error',
        duration: 5 * 1000
      });
      return Promise.reject(new Error(res.message || '未知错误'));
    }
    
    // 直接返回data部分
    return res.data;
  },
  (error) => {
    console.error('响应错误:', error);
    const message = error.response?.data?.message || error.message || '请求失败';
    ElMessage({
      message: message,
      type: 'error',
      duration: 5 * 1000
    });
    return Promise.reject(error);
  }
);

export default service;
```

#### 2.3 组件示例 - 项目列表

```vue
<!-- src/views/projects/Index.vue -->
<template>
  <div class="project-list">
    <div class="filter-bar">
      <el-form :inline="true" :model="filterForm">
        <el-form-item label="投资主体">
          <el-select v-model="filterForm.entity" clearable>
            <el-option
              v-for="item in entityOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="类别">
          <el-select v-model="filterForm.category" clearable>
            <el-option label="全部" value="" />
            <el-option label="开工项目" value="implementation" />
            <el-option label="逾期项目" value="delayed" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="fetchProjects">查询</el-button>
        </el-form-item>
      </el-form>
    </div>
    
    <el-table
      v-loading="loading"
      :data="projects"
      border
      style="width: 100%"
    >
      <el-table-column prop="project_code" label="项目编号" width="120" />
      <el-table-column prop="project_name" label="项目名称" />
      <el-table-column prop="investment_entity" label="投资主体" width="120" />
      <el-table-column prop="current_progress" label="当前进度" width="120" />
      <el-table-column label="操作" width="150">
        <template #default="scope">
          <el-button
            size="small"
            @click="handleView(scope.row)"
          >
            查看
          </el-button>
          <el-button
            size="small"
            type="primary"
            @click="handleEdit(scope.row)"
          >
            编辑
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <div class="pagination">
      <el-pagination
        :current-page="currentPage"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { getProjectList } from '@/api/projects';
import { getDatabaseOptions } from '@/api/options';

const router = useRouter();
const loading = ref(false);
const projects = ref([]);
const entityOptions = ref([]);
const total = ref(0);
const currentPage = ref(1);
const pageSize = ref(10);

const filterForm = reactive({
  entity: '',
  category: '',
});

onMounted(async () => {
  await fetchEntityOptions();
  await fetchProjects();
});

async function fetchEntityOptions() {
  try {
    // 获取投资主体选项
    const data = await getDatabaseOptions('investment_entity');
    entityOptions.value = data.map(item => ({
      label: item,
      value: item
    }));
  } catch (error) {
    console.error('获取投资主体选项失败:', error);
  }
}

async function fetchProjects() {
  loading.value = true;
  try {
    // 调用API获取项目列表
    const data = await getProjectList({
      entity: filterForm.entity,
      category: filterForm.category,
    });
    
    // 直接使用云函数返回的数据结构
    projects.value = data.projects || [];
    total.value = data.projects?.length || 0;
  } catch (error) {
    console.error('获取项目列表失败:', error);
  } finally {
    loading.value = false;
  }
}

function handleView(row) {
  router.push(`/projects/${row.project_code}`);
}

function handleEdit(row) {
  router.push(`/projects/${row.project_code}/edit`);
}

function handleSizeChange(val) {
  pageSize.value = val;
  fetchProjects();
}

function handleCurrentChange(val) {
  currentPage.value = val;
  fetchProjects();
}
</script>

<style scoped>
.project-list {
  padding: 20px;
  background-color: white;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}
.filter-bar {
  margin-bottom: 20px;
}
.pagination {
  margin-top: 20px;
  text-align: right;
}
</style>
```

### 3. 前端路由与后端API的映射

为了确保前端路由与后端API的一致性，我们需要建立清晰的映射关系：

| 前端路由 | 后端API | 描述 |
|---------|--------|------|
| /projects | /api/projects | 项目列表页面 |
| /projects/:id | /api/projects/:id | 项目详情页面 |
| /projects/:id/edit | /api/projects/:id | 项目编辑页面 |
| /dashboard | /api/dashboard/redblack | 仪表盘页面（红黑榜） |
| /dashboard/investment-details | /api/projects | 投资详情页面 |
| /timesheet | /api/timesheet | 工时管理页面 |
| /reports | /api/reports/weekly | 周报管理页面 |
| /settings | /api/options | 系统设置页面 |
| /users | /api/users | 用户管理页面 |
| /teams | /api/teams | 团队管理页面 |

### 4. 状态管理

使用Pinia进行状态管理，为每个主要模块创建独立的Store：

```javascript
// src/store/modules/project.js
import { defineStore } from 'pinia';
import { getProjectList, getProjectDetails } from '@/api/projects';

export const useProjectStore = defineStore('project', {
  state: () => ({
    projectList: [],
    currentProject: null,
    loading: false,
    error: null
  }),
  
  getters: {
    getProjectById: (state) => (id) => {
      return state.projectList.find(project => project.project_code === id) || null;
    }
  },
  
  actions: {
    async fetchProjects(params) {
      this.loading = true;
      this.error = null;
      try {
        const data = await getProjectList(params);
        this.projectList = data.projects || [];
        return data;
      } catch (error) {
        this.error = error.message || '获取项目列表失败';
        throw error;
      } finally {
        this.loading = false;
      }
    },
    
    async fetchProjectDetails(projectCode) {
      this.loading = true;
      this.error = null;
      try {
        const data = await getProjectDetails(projectCode);
        this.currentProject = data.project || null;
        return data;
      } catch (error) {
        this.error = error.message || '获取项目详情失败';
        throw error;
      } finally {
        this.loading = false;
      }
    }
  }
});
```

### 5. 环境变量配置

为了支持不同环境（开发、测试、生产）的API地址配置：

```
# .env.development
VITE_API_BASE_URL=http://localhost:8000

# .env.production
VITE_API_BASE_URL=https://api.pmo-system.com
```

### 6. 构建配置

使用Vite进行构建，配置如下：

```javascript
// vite.config.js
import { defineConfig } from 'vite';
import vue from '@vitejs/plugin-vue';
import path from 'path';

export default defineConfig({
  plugins: [vue()],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, 'src')
    }
  },
  server: {
    port: 3000,
    proxy: {
      '/api': {
        target: 'http://localhost:8000',
        changeOrigin: true
      }
    }
  },
  build: {
    outDir: 'dist',
    assetsDir: 'assets',
    sourcemap: false,
    // 使用国内镜像源加速构建
    chunkSizeWarningLimit: 1500
  }
});
```

### 7. 核心组件设计

#### 7.1 应用入口 (src/main.js)
- 初始化Vue应用
- 注册全局组件和插件
- 配置路由和状态管理
- 设置全局样式和主题

#### 7.2 API请求层 (src/api/)
- 使用Axios封装HTTP请求
- 处理请求拦截（添加认证令牌）
- 处理响应拦截（错误处理、数据转换）
- 为每个API模块提供独立的服务函数

#### 7.3 路由管理 (src/router/)
- 定义应用路由结构
- 实现路由守卫（认证检查）
- 处理路由过渡动画
- 支持路由懒加载

#### 7.4 状态管理 (src/store/)
- 使用Pinia管理应用状态
- 划分状态模块（用户、项目、设置等）
- 实现状态持久化（localStorage）
- 处理异步操作和API调用

#### 7.5 布局组件 (src/components/layout/)
- 实现主布局（侧边栏、顶栏、内容区）
- 支持响应式设计（适应不同屏幕尺寸）
- 提供导航菜单和面包屑
- 处理布局切换和主题

#### 7.6 业务组件 (src/components/business/)
- 项目列表和详情组件
- 工时录入和报表组件
- 红黑榜组件
- 用户管理组件

#### 7.7 工具函数 (src/utils/)
- 请求和认证工具
- 日期格式化和处理
- 表单验证
- 通用辅助函数

### 8. 页面设计

#### 8.1 认证页面
- 登录页面
- 忘记密码页面
- 修改密码页面

#### 8.2 仪表盘页面
- 项目统计概览
- 红黑榜展示
- 待办事项提醒
- 项目进度图表

#### 8.3 投资详情页面
- 投资金额统计概览
- 项目列表详细展示
- 按投资主体和类别筛选
- 数据导出功能

#### 8.4 项目管理页面
- 项目列表（支持筛选和排序）
- 项目详情（基本信息、进度、文档等）
- 项目编辑表单
- 项目变更历史

#### 8.5 工时管理页面
- 工时录入表单
- 工时统计报表
- 工时审批流程

#### 8.6 报告管理页面
- 周报生成和查看
- 项目报告模板
- 报告导出功能

## 四、桌面应用架构设计 (Electron)

### 1. 目录结构
```
electron/
├── dist/                   # 构建输出目录
├── node_modules/           # 依赖包
├── build/                  # 构建相关
│   ├── icons/              # 应用图标
│   └── installer.js        # 安装程序配置
├── src/                    # 源代码
│   ├── main/               # 主进程
│   │   ├── index.js        # 主进程入口
│   │   ├── ipc.js          # IPC通信
│   │   ├── menu.js         # 应用菜单
│   │   ├── tray.js         # 系统托盘
│   │   └── updater.js      # 自动更新
│   ├── preload/            # 预加载脚本
│   │   └── index.js        # 预加载入口
│   └── renderer/           # 渲染进程 (Web应用)
├── .env                    # 环境变量
├── electron-builder.yml    # Electron Builder配置
├── package.json            # 项目配置
└── README.md               # 项目说明
```

### 2. 桌面应用与云函数适配

桌面应用本质上是将Web应用封装到Electron中，因此与Web应用对云函数的适配方式类似。但桌面应用有一些特殊的需求，如离线支持、系统托盘集成等。

#### 2.1 离线数据缓存

为了支持离线工作，需要在本地缓存关键数据：

```javascript
// src/main/db.js
const { app } = require('electron');
const path = require('path');
const fs = require('fs');
const util = require('util');

const readFile = util.promisify(fs.readFile);
const writeFile = util.promisify(fs.writeFile);
const mkdir = util.promisify(fs.mkdir);

// 数据存储目录
const dataDir = path.join(app.getPath('userData'), 'data');

// 确保数据目录存在
async function ensureDataDir() {
  try {
    await mkdir(dataDir, { recursive: true });
  } catch (err) {
    if (err.code !== 'EEXIST') throw err;
  }
}

// 保存数据到本地
async function saveData(key, data) {
  await ensureDataDir();
  const filePath = path.join(dataDir, `${key}.json`);
  await writeFile(filePath, JSON.stringify(data), 'utf8');
}

// 从本地加载数据
async function loadData(key) {
  await ensureDataDir();
  const filePath = path.join(dataDir, `${key}.json`);
  try {
    const data = await readFile(filePath, 'utf8');
    return JSON.parse(data);
  } catch (err) {
    if (err.code === 'ENOENT') return null;
    throw err;
  }
}

module.exports = {
  saveData,
  loadData
};
```

#### 2.2 请求队列管理

在离线状态下，将用户的操作保存到队列中，等网络恢复后再同步：

```javascript
// src/renderer/utils/request-queue.js
import { ipcRenderer } from 'electron';
import localforage from 'localforage';

// 初始化请求队列存储
const requestQueue = localforage.createInstance({
  name: 'requestQueue'
});

// 添加请求到队列
export async function addToQueue(request) {
  const id = Date.now().toString();
  await requestQueue.setItem(id, {
    id,
    request,
    timestamp: Date.now(),
    status: 'pending'
  });
  return id;
}

// 处理队列中的请求
export async function processQueue() {
  // 获取所有待处理的请求
  const requests = [];
  await requestQueue.iterate((value, key) => {
    if (value.status === 'pending') {
      requests.push({ key, value });
    }
  });

  // 按时间戳排序
  requests.sort((a, b) => a.value.timestamp - b.value.timestamp);

  // 依次处理请求
  for (const { key, value } of requests) {
    try {
      // 更新状态为处理中
      await requestQueue.setItem(key, {
        ...value,
        status: 'processing'
      });

      // 发送请求
      const response = await fetch(value.request.url, {
        method: value.request.method,
        headers: value.request.headers,
        body: value.request.body
      });

      // 请求成功，从队列中移除
      await requestQueue.removeItem(key);

      // 通知渲染进程请求已处理
      ipcRenderer.send('request-processed', {
        id: key,
        success: true
      });
    } catch (error) {
      // 请求失败，更新状态
      await requestQueue.setItem(key, {
        ...value,
        status: 'failed',
        error: error.message
      });

      // 通知渲染进程请求失败
      ipcRenderer.send('request-processed', {
        id: key,
        success: false,
        error: error.message
      });
    }
  }
}

// 监听网络状态变化
window.addEventListener('online', () => {
  processQueue();
});
```

#### 2.3 系统托盘集成

```javascript
// src/main/tray.js
const { app, Tray, Menu, nativeImage } = require('electron');
const path = require('path');

let tray = null;

function createTray(mainWindow) {
  // 创建托盘图标
  const iconPath = path.join(__dirname, '../assets/tray-icon.png');
  const trayIcon = nativeImage.createFromPath(iconPath).resize({ width: 16, height: 16 });
  
  tray = new Tray(trayIcon);
  
  // 设置托盘菜单
  const contextMenu = Menu.buildFromTemplate([
    {
      label: '打开PMO系统',
      click: () => {
        mainWindow.show();
      }
    },
    {
      label: '刷新数据',
      click: () => {
        mainWindow.webContents.send('refresh-data');
      }
    },
    { type: 'separator' },
    {
      label: '退出',
      click: () => {
        app.quit();
      }
    }
  ]);
  
  tray.setToolTip('PMO系统');
  tray.setContextMenu(contextMenu);
  
  // 点击托盘图标显示/隐藏主窗口
  tray.on('click', () => {
    if (mainWindow.isVisible()) {
      mainWindow.hide();
    } else {
      mainWindow.show();
    }
  });
  
  return tray;
}

module.exports = {
  createTray
};
```

#### 2.4 自动更新机制

```javascript
// src/main/updater.js
const { app, dialog } = require('electron');
const { autoUpdater } = require('electron-updater');
const log = require('electron-log');

// 配置日志
log.transports.file.level = 'info';
autoUpdater.logger = log;

// 检查更新
function checkForUpdates() {
  autoUpdater.checkForUpdatesAndNotify();
}

// 配置自动更新事件
function setupAutoUpdater(mainWindow) {
  // 更新下载完成
  autoUpdater.on('update-downloaded', (info) => {
    const dialogOpts = {
      type: 'info',
      buttons: ['立即重启', '稍后'],
      title: '应用更新',
      message: '有新版本可用',
      detail: `PMO系统 ${info.version} 已下载，重启应用以应用更新。`
    };
    
    dialog.showMessageBox(dialogOpts).then((returnValue) => {
      if (returnValue.response === 0) {
        autoUpdater.quitAndInstall();
      }
    });
  });
  
  // 更新错误
  autoUpdater.on('error', (err) => {
    log.error('更新错误', err);
  });
  
  // 检查更新
  app.on('ready', () => {
    checkForUpdates();
  });
}

module.exports = {
  setupAutoUpdater,
  checkForUpdates
};
```

#### 2.5 主进程与渲染进程通信

```javascript
// src/main/ipc.js
const { ipcMain } = require('electron');
const { saveData, loadData } = require('./db');

function setupIPC(mainWindow) {
  // 保存数据到本地
  ipcMain.handle('save-data', async (event, { key, data }) => {
    try {
      await saveData(key, data);
      return { success: true };
    } catch (error) {
      return { success: false, error: error.message };
    }
  });
  
  // 从本地加载数据
  ipcMain.handle('load-data', async (event, { key }) => {
    try {
      const data = await loadData(key);
      return { success: true, data };
    } catch (error) {
      return { success: false, error: error.message };
    }
  });
  
  // 检查网络状态
  ipcMain.handle('check-online', () => {
    return { online: mainWindow.webContents.getURL() !== '' };
  });
}

module.exports = {
  setupIPC
};
```

#### 2.6 预加载脚本

```javascript
// src/preload/index.js
const { contextBridge, ipcRenderer } = require('electron');

// 暴露安全的API给渲染进程
contextBridge.exposeInMainWorld('electronAPI', {
  // 数据存储
  saveData: (key, data) => ipcRenderer.invoke('save-data', { key, data }),
  loadData: (key) => ipcRenderer.invoke('load-data', { key }),
  
  // 网络状态
  checkOnline: () => ipcRenderer.invoke('check-online'),
  
  // 事件监听
  onRefreshData: (callback) => {
    ipcRenderer.on('refresh-data', callback);
    return () => ipcRenderer.removeListener('refresh-data', callback);
  },
  
  // 请求处理
  onRequestProcessed: (callback) => {
    ipcRenderer.on('request-processed', (event, result) => callback(result));
    return () => ipcRenderer.removeListener('request-processed', callback);
  }
});
```

#### 2.7 主进程入口

```javascript
// src/main/index.js
const { app, BrowserWindow, Menu } = require('electron');
const path = require('path');
const { createTray } = require('./tray');
const { setupAutoUpdater } = require('./updater');
const { setupIPC } = require('./ipc');

// 禁用默认菜单
Menu.setApplicationMenu(null);

// 保持对窗口对象的全局引用
let mainWindow = null;

function createWindow() {
  // 创建浏览器窗口
  mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    webPreferences: {
      preload: path.join(__dirname, '../preload/index.js'),
      contextIsolation: true,
      nodeIntegration: false
    },
    icon: path.join(__dirname, '../assets/icon.png')
  });

  // 加载应用
  if (app.isPackaged) {
    mainWindow.loadFile(path.join(__dirname, '../renderer/index.html'));
  } else {
    mainWindow.loadURL('http://localhost:3000');
    // 打开开发者工具
    mainWindow.webContents.openDevTools();
  }

  // 窗口关闭时只是隐藏窗口，不退出应用
  mainWindow.on('close', (event) => {
    if (!app.isQuitting) {
      event.preventDefault();
      mainWindow.hide();
      return false;
    }
    return true;
  });
}

// 应用准备就绪时创建窗口
app.whenReady().then(() => {
  createWindow();
  createTray(mainWindow);
  setupIPC(mainWindow);
  setupAutoUpdater(mainWindow);
});

// 所有窗口关闭时退出应用（macOS除外）
app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('activate', () => {
  // macOS中点击Dock图标时没有已打开的窗口则创建一个窗口
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow();
  }
});

// 设置退出标志
app.on('before-quit', () => {
  app.isQuitting = true;
});
```

### 3. 与云函数的数据同步策略

桌面应用需要处理离线和在线两种状态下的数据同步问题：

#### 3.1 在线状态

1. **直接请求API**：与Web应用相同，直接请求后端API
2. **数据缓存**：将获取的数据缓存到本地，以便离线使用
3. **定期同步**：定期检查数据更新，保持本地数据最新

#### 3.2 离线状态

1. **使用缓存数据**：从本地加载缓存的数据
2. **请求队列**：将修改操作添加到请求队列
3. **恢复在线时同步**：网络恢复后，按顺序处理队列中的请求

#### 3.3 数据冲突解决

1. **时间戳比较**：使用最后修改时间判断哪个版本更新
2. **版本号机制**：为每次修改分配递增的版本号
3. **冲突提示**：当检测到冲突时，提示用户选择保留哪个版本

### 4. 打包与分发

```yaml
# electron-builder.yml
appId: com.company.pmo
productName: PMO系统
directories:
  output: dist
  buildResources: build
files:
  - from: dist
    to: .
    filter:
      - "**/*"
  - from: build
    to: build
    filter:
      - "**/*"
publish:
  provider: generic
  url: https://your-update-server.com/
  channel: latest
win:
  target:
    - target: nsis
      arch:
        - x64
  icon: build/icons/icon.ico
  publisherName: Your Company
nsis:
  oneClick: false
  allowToChangeInstallationDirectory: true
  createDesktopShortcut: true
  createStartMenuShortcut: true
  shortcutName: PMO系统
  uninstallDisplayName: PMO系统
mac:
  target:
    - target: dmg
      arch:
        - x64
        - arm64
  icon: build/icons/icon.icns
  category: public.app-category.business
  hardenedRuntime: true
  gatekeeperAssess: false
linux:
  target:
    - target: AppImage
      arch:
        - x64
  icon: build/icons
  category: Office
```

### 5. 自动更新配置

```javascript
// package.json
{
  "name": "pmo-electron",
  "version": "1.0.0",
  "description": "PMO系统桌面客户端",
  "main": "dist/main/index.js",
  "scripts": {
    "dev": "vite",
    "build": "vite build && electron-builder",
    "build:dir": "vite build && electron-builder --dir",
    "build:win": "vite build && electron-builder --win",
    "build:mac": "vite build && electron-builder --mac",
    "build:linux": "vite build && electron-builder --linux",
    "postinstall": "electron-builder install-app-deps"
  },
  "dependencies": {
    "electron-log": "^4.4.8",
    "electron-updater": "^5.3.0",
    "localforage": "^1.10.0"
  },
  "devDependencies": {
    "electron": "^22.0.0",
    "electron-builder": "^23.6.0",
    "vite": "^4.0.0",
    "vite-plugin-electron": "^0.11.1"
  }
}
```

## 五、移动应用架构设计 (Flutter)

### 1. 目录结构
```
flutter_app/
├── android/                # Android原生代码
├── ios/                    # iOS原生代码
├── lib/                    # Dart源代码
│   ├── api/                # API服务
│   │   ├── api_client.dart # API客户端
│   │   ├── auth_api.dart   # 认证API
│   │   ├── project_api.dart # 项目API
│   │   └── ...             # 其他API
│   ├── models/             # 数据模型
│   │   ├── user.dart       # 用户模型
│   │   ├── project.dart    # 项目模型
│   │   └── ...             # 其他模型
│   ├── screens/            # 页面
│   │   ├── auth/           # 认证页面
│   │   ├── dashboard/      # 仪表盘页面
│   │   ├── projects/       # 项目页面
│   │   └── ...             # 其他页面
│   ├── widgets/            # UI组件
│   │   ├── common/         # 通用组件
│   │   └── business/       # 业务组件
│   ├── services/           # 业务服务
│   │   ├── auth_service.dart # 认证服务
│   │   └── ...             # 其他服务
│   ├── utils/              # 工具类
│   │   ├── date_utils.dart # 日期工具
│   │   └── ...             # 其他工具
│   ├── routes.dart         # 路由配置
│   └── main.dart           # 应用入口
├── assets/                 # 资源文件
├── pubspec.yaml            # 项目配置
└── README.md               # 项目说明
```

### 2. 核心功能设计

#### 2.1 应用入口 (lib/main.dart)
- 初始化应用
- 配置主题和样式
- 设置路由和状态管理
- 处理应用生命周期

#### 2.2 API服务层 (lib/api/)
- 使用Dio封装HTTP请求
- 处理认证和令牌管理
- 实现请求重试和错误处理
- 支持离线操作和数据同步

#### 2.3 状态管理
- 使用Provider/Riverpod管理应用状态
- 实现响应式UI更新
- 处理异步操作和加载状态
- 支持状态持久化

#### 2.4 UI设计
- 遵循Material Design 3设计语言
- 实现响应式布局（适应不同屏幕尺寸）
- 支持深色模式和主题切换
- 优化触摸交互和手势操作

#### 2.5 离线支持
- 使用Hive/SQLite存储本地数据
- 实现增量同步机制
- 处理网络状态变化
- 解决数据冲突

#### 2.6 移动特有功能
- 推送通知集成
- 生物认证（指纹/面容识别）
- 相机和文件访问
- 后台同步服务

## 六、部署方案

### 1. 后端部署

#### 1.1 Docker容器化
- 使用Docker打包应用和依赖
- 配置环境变量和卷挂载
- 设置健康检查和重启策略
- 优化容器镜像大小

#### 1.2 Docker Compose配置
```yaml
# docker-compose.yml
version: '3'

services:
  api:
    build: .
    restart: always
    ports:
      - "8000:8000"
    environment:
      - DB_HOST=${DB_HOST}
      - DB_PORT=${DB_PORT}
      - DB_USER=${DB_USER}
      - DB_PASSWORD=${DB_PASSWORD}
      - DB_NAME=${DB_NAME}
      - SECRET_KEY=${SECRET_KEY}
    depends_on:
      - db
  
  db:
    image: mysql:8
    volumes:
      - mysql_data:/var/lib/mysql
    environment:
      - MYSQL_ROOT_PASSWORD=${DB_ROOT_PASSWORD}
      - MYSQL_DATABASE=${DB_NAME}
      - MYSQL_USER=${DB_USER}
      - MYSQL_PASSWORD=${DB_PASSWORD}
    command: --default-authentication-plugin=mysql_native_password
  
  nginx:
    image: nginx:latest
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/conf.d:/etc/nginx/conf.d
      - ./nginx/ssl:/etc/nginx/ssl
      - ./frontend/dist:/usr/share/nginx/html
    depends_on:
      - api

volumes:
  mysql_data:
```

#### 1.3 生产环境配置
- 配置HTTPS和SSL证书
- 设置反向代理和负载均衡
- 实现日志收集和监控
- 配置自动备份策略

### 2. 前端部署

#### 2.1 Web应用部署
- 构建静态资源（HTML、CSS、JS）
- 配置Nginx服务器
- 设置缓存策略和压缩
- 实现CDN分发

#### 2.2 桌面应用分发
- 使用Electron Builder打包应用
- 配置自动更新服务
- 生成安装程序和便携版
- 实现数字签名和验证

#### 2.3 移动应用发布
- 生成Android APK和App Bundle
- 准备iOS App Store发布包
- 配置应用内更新机制
- 设置推送通知服务

## 七、开发计划

### 1. 阶段一：基础设施搭建和云函数迁移（3周）
- 搭建FastAPI项目结构
- 配置数据库连接和环境变量
- 迁移核心云函数到服务层
- 实现基础API路由和认证系统

### 2. 阶段二：Web应用开发（4周）
- 搭建Vue项目结构
- 实现核心页面和组件
- 开发项目管理和红黑榜模块
- 实现工时和报表功能

### 3. 阶段三：桌面应用开发（2周）
- 配置Electron项目
- 集成Web应用到Electron
- 实现系统托盘和通知功能
- 添加离线支持和自动更新

### 4. 阶段四：移动应用开发（4周）
- 搭建Flutter项目结构
- 实现核心页面和组件
- 开发移动特有功能
- 优化离线体验和性能

### 5. 阶段五：测试与部署（2周）
- 编写单元测试和集成测试
- 进行性能测试和兼容性测试
- 配置部署环境和CI/CD流程
- 准备用户文档和培训材料

## 八、风险与应对措施

### 1. 技术风险
- **云函数迁移不完整**：编写详细测试用例，确保功能一致性
- **多平台适配复杂**：采用响应式设计，建立统一设计规范
- **离线同步冲突**：实现版本控制和冲突解决策略
- **性能瓶颈**：进行性能测试，优化关键路径

### 2. 项目风险
- **需求变更**：采用敏捷开发方法，增量交付
- **进度延误**：设置合理里程碑，优先实现核心功能
- **团队协作**：建立清晰的接口文档和开发规范
- **用户接受度**：提前收集用户反馈，进行用户测试

## 九、结论

本PMO系统改造方案通过将现有云函数代码迁移到统一的后端API，并开发多端前端应用，实现了系统的全平台覆盖。方案保留了原有系统的业务逻辑，同时提升了用户体验和系统可用性。通过分阶段开发和部署，可以有效控制项目风险，确保系统稳定可靠。多端支持将使PMO系统更好地适应现代办公环境，满足用户随时随地工作的需求，提高工作效率和团队协作能力。

