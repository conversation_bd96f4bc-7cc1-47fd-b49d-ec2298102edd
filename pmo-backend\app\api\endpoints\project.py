from fastapi import APIRouter, Depends, HTTPException, Query, Path, Body, UploadFile, File
from fastapi.responses import StreamingResponse
from typing import Dict, Any, Optional, List
import json
import os
import pandas as pd
import io
from datetime import datetime
from pydantic import BaseModel

from app.core.security import get_current_user
from app.core.logger import get_logger
from app.core.database import get_db_connection
from app.core.permissions import PermissionManager

# 导入云函数
from app.cloud_functions.getProjectList.index import main_handler as get_project_list_handler
from app.cloud_functions.updateProjectDetails.index import main_handler as update_project_details_handler
from app.cloud_functions.updateProjectAccountBook.index import main_handler as update_project_account_book_handler

# 导入档案统计函数
from app.api.endpoints.project_archive import get_project_archive_status_from_files

logger = get_logger(__name__)
router = APIRouter()

@router.get("/all")
async def get_all_projects(
    investment_entity: Optional[str] = Query(None, description="投资主体筛选"),
    project_name: Optional[str] = Query(None, description="项目名称搜索"),
    current_progress: Optional[str] = Query(None, description="当前进度筛选"),
    category_level2: Optional[str] = Query(None, description="规划类别筛选"),
    current_user: dict = Depends(get_current_user)
):
    """获取所有项目列表（无分页）"""
    try:
        conn = get_db_connection()
        with conn.cursor() as cursor:
            # 构建查询条件
            where_conditions = []
            params = []

            if investment_entity:
                where_conditions.append("investment_entity = %s")
                params.append(investment_entity)

            if project_name:
                where_conditions.append("project_name LIKE %s")
                params.append(f"%{project_name}%")

            if current_progress:
                where_conditions.append("current_progress = %s")
                params.append(current_progress)

            if category_level2:
                where_conditions.append("category_level2 = %s")
                params.append(category_level2)

            where_clause = " WHERE " + " AND ".join(where_conditions) if where_conditions else ""

            # 获取所有数据
            data_sql = f"""
                SELECT * FROM project_account_book
                {where_clause}
                ORDER BY project_code ASC
            """
            cursor.execute(data_sql, params)
            projects = cursor.fetchall()

        conn.close()

        # 根据用户权限过滤项目列表
        filtered_projects = PermissionManager.filter_projects_by_permission(projects, current_user)

        # 为每个项目添加档案状态信息（复用项目档案页面的逻辑）
        projects_with_archive_status = []
        for project in filtered_projects:
            try:
                # 获取项目档案状态（使用与项目档案页面相同的函数）
                archive_status = get_project_archive_status_from_files(project.get('project_code', ''))
                project_data = dict(project)
                project_data['archive_status'] = archive_status

                # 为了兼容前端，同时提供简化的档案数量信息
                file_count_by_type = archive_status.get('file_count_by_type', {})
                markdown_files = file_count_by_type.get('.md', 0)
                total_files = archive_status.get('total_files', 0)

                project_data['archive_count'] = {
                    "total_files": total_files,
                    "markdown_files": markdown_files,
                    "other_files": total_files - markdown_files,
                    "archive_summary": archive_status.get('archive_summary', '暂无档案')
                }

                projects_with_archive_status.append(project_data)
            except Exception as e:
                logger.error(f"获取项目 {project.get('project_code')} 档案状态失败: {str(e)}")
                # 添加默认的档案状态信息
                project_data = dict(project)
                project_data['archive_status'] = {
                    "total_files": 0,
                    "total_size": 0,
                    "stages": {},
                    "latest_file_time": None,
                    "archive_summary": "获取失败"
                }
                project_data['archive_count'] = {
                    "total_files": 0,
                    "markdown_files": 0,
                    "other_files": 0,
                    "archive_summary": "获取失败"
                }
                projects_with_archive_status.append(project_data)

        total = len(projects_with_archive_status)
        logger.info(f"用户 {current_user.get('username', 'unknown')} (role={current_user.get('role')}) 获取项目列表: {total} 个项目")

        return {
            "code": 200,
            "success": True,
            "message": "获取项目列表成功",
            "data": {
                "projects": projects_with_archive_status,
                "total": total
            }
        }

    except Exception as e:
        logger.error(f"获取项目列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取项目列表失败: {str(e)}")

@router.get("/detail/{project_id}")
async def get_project_detail_management(project_id: int):
    """获取项目详情（完整）"""
    try:
        conn = get_db_connection()
        with conn.cursor() as cursor:
            cursor.execute("SELECT * FROM project_account_book WHERE id = %s", (project_id,))
            project = cursor.fetchone()

        conn.close()

        if not project:
            raise HTTPException(status_code=404, detail="项目不存在")

        return {
            "code": 200,
            "success": True,
            "message": "获取项目详情成功",
            "data": project
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取项目详情失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取项目详情失败: {str(e)}")

@router.get("/list")
async def get_project_list(
    entity: Optional[str] = Query(None, description="投资主体"),
    implementation: Optional[bool] = Query(False, description="是否只获取开工项目"),
    delayed: Optional[bool] = Query(False, description="是否只获取逾期项目"),
    current_user: Dict = Depends(get_current_user)
) -> Dict[str, Any]:
    """
    获取项目列表
    """
    try:
        # 记录请求参数
        logger.info(f"获取项目列表 - 参数: entity={entity}, implementation={implementation}, delayed={delayed}")
        
        # 构造云函数需要的event对象
        event = {
            "queryString": {
                "entity": entity or "",
                "implementation": implementation,
                "delayed": delayed
            }
        }
        
        # 调用云函数
        logger.info(f"调用云函数 getProjectList - 参数: {event}")
        result = get_project_list_handler(event, None)
        logger.info(f"云函数返回状态码: {result.get('statusCode')}")
        
        # 解析云函数响应
        if result.get("statusCode") == 200:
            body = json.loads(result.get("body", "{}"))

            # 记录返回的项目数量
            if 'data' in body and isinstance(body['data'], list):
                projects = body['data']
                logger.info(f"云函数返回 {len(projects)} 个项目")

                # 根据用户权限过滤项目列表
                filtered_projects = PermissionManager.filter_projects_by_permission(projects, current_user)

                # 为每个项目添加档案状态信息
                projects_with_archive_status = []
                for project in filtered_projects:
                    try:
                        # 获取项目档案状态（使用与项目档案页面相同的函数）
                        archive_status = get_project_archive_status_from_files(project.get('project_code', ''))
                        project_data = dict(project)
                        project_data['archive_status'] = archive_status

                        # 为了兼容前端，同时提供简化的档案数量信息
                        file_count_by_type = archive_status.get('file_count_by_type', {})
                        markdown_files = file_count_by_type.get('.md', 0)
                        total_files = archive_status.get('total_files', 0)

                        project_data['archive_count'] = {
                            "total_files": total_files,
                            "markdown_files": markdown_files,
                            "other_files": total_files - markdown_files,
                            "archive_summary": archive_status.get('archive_summary', '暂无档案')
                        }

                        projects_with_archive_status.append(project_data)
                    except Exception as e:
                        logger.error(f"获取项目 {project.get('project_code')} 档案状态失败: {str(e)}")
                        # 添加默认的档案状态信息
                        project_data = dict(project)
                        project_data['archive_status'] = {
                            "total_files": 0,
                            "total_size": 0,
                            "stages": {},
                            "latest_file_time": None,
                            "archive_summary": "获取失败"
                        }
                        project_data['archive_count'] = {
                            "total_files": 0,
                            "markdown_files": 0,
                            "other_files": 0,
                            "archive_summary": "获取失败"
                        }
                        projects_with_archive_status.append(project_data)

                body['data'] = projects_with_archive_status

                logger.info(f"用户 {current_user.get('username', 'unknown')} (role={current_user.get('role')}) 权限过滤后: {len(projects_with_archive_status)} 个项目，已添加档案统计信息")

            return body
        else:
            logger.error(f"获取项目列表失败: {result}")
            raise HTTPException(status_code=result.get("statusCode", 500), detail="获取项目列表失败")
    except Exception as e:
        logger.error(f"获取项目列表异常: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/{project_id}")
async def get_project_detail(
    project_id: str,
    current_user: Dict = Depends(get_current_user)
) -> Dict[str, Any]:
    """
    获取项目详情
    """
    try:
        # 构造云函数需要的event对象
        event = {
            "queryString": {
                "project_id": project_id
            }
        }
        
        # 调用云函数
        result = get_project_list_handler(event, None)
        
        # 解析云函数响应
        if result.get("statusCode") == 200:
            body = json.loads(result.get("body", "{}"))
            
            # 如果返回了项目列表，取第一个项目作为详情
            if body.get("code") == 200 and body.get("data") and isinstance(body["data"], list) and len(body["data"]) > 0:
                return {
                    "code": 200,
                    "message": "success",
                    "data": body["data"][0]
                }
            elif body.get("code") == 200:
                return body
            else:
                logger.error(f"获取项目详情失败: {body}")
                raise HTTPException(status_code=404, detail="项目不存在")
        else:
            logger.error(f"获取项目详情失败: {result}")
            raise HTTPException(status_code=result.get("statusCode", 500), detail="获取项目详情失败")
    except Exception as e:
        logger.error(f"获取项目详情异常: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/{project_code}/update")
async def update_project(
    project_code: str = Path(..., description="项目编号"),
    project_data: Dict = Body(..., description="项目数据"),
    current_user: Dict = Depends(get_current_user)
) -> Dict[str, Any]:
    """
    更新项目信息(POST方法)
    """
    try:
        # 记录请求参数
        logger.info(f"更新项目 - 项目编号: {project_code}")
        
        # 添加项目编号到数据中
        data = {"project_code": project_code, **project_data}
        
        # 构造云函数需要的event对象
        event = {
            "body": json.dumps(data)
        }
        
        # 调用云函数
        logger.info(f"调用云函数 updateProjectDetails - 参数: {event}")
        result = update_project_details_handler(event, None)
        logger.info(f"云函数返回状态码: {result.get('statusCode')}")
        
        # 解析云函数响应
        if result.get("statusCode") == 200:
            body = json.loads(result.get("body", "{}"))
            return {
                "code": 200,
                "message": "项目更新成功",
                "data": body.get("data", {})
            }
        else:
            logger.error(f"更新项目失败: {result}")
            raise HTTPException(status_code=result.get("statusCode", 500), detail="更新项目失败")
    except Exception as e:
        logger.error(f"更新项目异常: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e)) 

@router.post("/create")
async def create_project(
    project_data: Dict = Body(..., description="项目数据"),
    current_user: Dict = Depends(get_current_user)
) -> Dict[str, Any]:
    """
    创建新项目
    """
    try:
        # 记录请求参数
        logger.info(f"创建新项目 - 数据: {project_data}")
        
        # 从请求体中提取操作类型，确保在顶层
        operation = project_data.pop("operation", "add")
        
        # 添加用户信息
        if current_user:
            project_data["UserID"] = current_user.get("id", "")
            project_data["name"] = current_user.get("name", "")
        
        # 构造云函数需要的event对象，不要嵌套在body中
        event = {
            "operation": operation,
            **project_data  # 直接展开项目数据到顶层
        }
        
        # 调用云函数
        logger.info(f"调用云函数 updateProjectAccountBook - 参数: {event}")
        result = update_project_account_book_handler(event, None)
        logger.info(f"云函数返回状态码: {result.get('statusCode')}")
        
        # 直接返回云函数的结果，不再尝试解析
        if isinstance(result, dict):
            # 如果result是字典并且有body字段，尝试解析body
            if "body" in result:
                try:
                    return json.loads(result["body"])
                except json.JSONDecodeError:
                    return {"code": 200, "message": "操作成功，但无法解析响应"}
            # 如果没有body字段但有code字段，直接返回
            elif "code" in result:
                return result
            # 其他情况，返回通用成功响应
            else:
                return {"code": 200, "message": "操作成功", "data": result}
        else:
            # 如果不是字典，返回通用成功响应
            return {"code": 200, "message": "操作成功"}
    except Exception as e:
        logger.error(f"创建项目异常: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e)) 

@router.delete("/{project_code}")
async def delete_project_by_id(
    project_code: str = Path(..., description="项目编号"),
    current_user: Dict = Depends(get_current_user)
) -> Dict[str, Any]:
    """
    删除项目（DELETE方法）
    """
    try:
        # 记录请求参数
        logger.info(f"删除项目 - 项目编号: {project_code}")
        
        # 构造云函数需要的event对象
        event = {
            "operation": "delete",
            "project_code": project_code,
            "UserID": current_user.get("id", ""),
            "name": current_user.get("name", "")
        }
        
        # 调用云函数
        logger.info(f"调用云函数 updateProjectAccountBook - 参数: {event}")
        result = update_project_account_book_handler(event, None)
        logger.info(f"云函数返回状态码: {result.get('statusCode')}")
        
        # 直接返回云函数的结果，不再尝试解析
        if isinstance(result, dict):
            # 如果result是字典并且有body字段，尝试解析body
            if "body" in result:
                try:
                    return json.loads(result["body"])
                except json.JSONDecodeError:
                    return {"code": 200, "message": "操作成功，但无法解析响应"}
            # 如果没有body字段但有code字段，直接返回
            elif "code" in result:
                return result
            # 其他情况，返回通用成功响应
            else:
                return {"code": 200, "message": "操作成功", "data": result}
        else:
            # 如果不是字典，返回通用成功响应
            return {"code": 200, "message": "操作成功"}
    except Exception as e:
        logger.error(f"删除项目异常: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/{project_code}/ai-update")
async def ai_update_project(
    project_code: str = Path(..., description="项目编号")
) -> Dict[str, Any]:
    """
    AI更新项目信息 - 基于项目档案中的markdown文件（分步骤展示过程）
    """
    try:
        logger.info(f"开始AI更新项目: {project_code}")

        # 初始化步骤跟踪
        steps_info = {
            "step1_scan_archives": {"status": "processing", "message": "正在扫描项目档案...", "data": None},
            "step2_extract_content": {"status": "pending", "message": "等待提取文档内容", "data": None},
            "step3_get_project_info": {"status": "pending", "message": "等待获取项目记录", "data": None},
            "step4_generate_prompt": {"status": "pending", "message": "等待生成AI提示词", "data": None},
            "step5_ai_analysis": {"status": "pending", "message": "等待AI分析", "data": None},
            "step6_user_confirm": {"status": "pending", "message": "等待用户确认", "data": None}
        }

        # 步骤1：扫描项目档案
        logger.info(f"步骤1：扫描项目档案 - {project_code}")
        archive_dir = f"project_archive_materials/{project_code}"
        markdown_files = []
        archive_stages = []
        scanned_files = []

        if os.path.exists(archive_dir):
            # 检查存在的档案阶段文件夹
            stage_mapping = {
                "项目立项": "立项阶段",
                "任务采购": "采购阶段",
                "项目实施": "实施阶段",
                "项目验收": "验收阶段",
                "项目结项": "结项阶段"
            }

            for stage_folder in os.listdir(archive_dir):
                stage_path = os.path.join(archive_dir, stage_folder)
                if os.path.isdir(stage_path) and stage_folder in stage_mapping:
                    archive_stages.append(stage_mapping[stage_folder])

            # 扫描markdown文件
            for root, dirs, files in os.walk(archive_dir):
                for file in files:
                    if file.endswith('.md'):
                        file_path = os.path.join(root, file)
                        relative_path = os.path.relpath(file_path, archive_dir)
                        file_size = os.path.getsize(file_path)
                        scanned_files.append({
                            "filename": file,
                            "relative_path": relative_path,
                            "size_bytes": file_size,
                            "size_kb": round(file_size / 1024, 2)
                        })

        steps_info["step1_scan_archives"] = {
            "status": "completed",
            "message": f"扫描完成，找到 {len(scanned_files)} 个Markdown文件",
            "data": {
                "archive_directory": archive_dir,
                "archive_stages": archive_stages,
                "markdown_files_found": scanned_files,
                "total_files": len(scanned_files)
            }
        }

        if len(scanned_files) == 0:
            return {
                "code": 400,
                "message": "该项目没有可用的档案文件",
                "data": {"steps": steps_info}
            }

        # 步骤2：提取文档内容
        logger.info(f"步骤2：提取文档内容")
        steps_info["step2_extract_content"]["status"] = "processing"
        steps_info["step2_extract_content"]["message"] = "正在提取文档内容..."

        extracted_files = []
        total_content_length = 0

        for file_info in scanned_files:
            file_path = os.path.join(archive_dir, file_info["relative_path"])
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    content_length = len(content)
                    total_content_length += content_length

                    extracted_files.append({
                        "filename": file_info["filename"],
                        "relative_path": file_info["relative_path"],
                        "content": content,
                        "content_length": content_length,
                        "content_preview": content[:200] + "..." if len(content) > 200 else content
                    })
                    markdown_files.append({
                        "filename": file_info["filename"],
                        "content": content,
                        "path": file_path
                    })
            except Exception as e:
                logger.warning(f"读取markdown文件失败 {file_path}: {str(e)}")
                extracted_files.append({
                    "filename": file_info["filename"],
                    "relative_path": file_info["relative_path"],
                    "error": str(e),
                    "content_length": 0
                })

        steps_info["step2_extract_content"] = {
            "status": "completed",
            "message": f"内容提取完成，共提取 {len(extracted_files)} 个文件，总字符数：{total_content_length:,}",
            "data": {
                "extracted_files": extracted_files,
                "total_content_length": total_content_length,
                "successful_extractions": len([f for f in extracted_files if "error" not in f])
            }
        }

        # 步骤3：获取项目记录
        logger.info(f"步骤3：获取项目记录")
        steps_info["step3_get_project_info"]["status"] = "processing"
        steps_info["step3_get_project_info"]["message"] = "正在获取项目记录..."

        from app.db.connection import get_db_connection
        conn = get_db_connection()
        cursor = conn.cursor()
        cursor.execute("""
            SELECT * FROM project_account_book
            WHERE project_code = %s
        """, (project_code,))
        project_info = cursor.fetchone()

        if not project_info:
            conn.close()
            raise HTTPException(status_code=404, detail="项目不存在")
        conn.close()

        # 构建当前项目字段信息（包含所有重要字段）
        current_fields = {
            # 基本信息
            "project_name": project_info.get("project_name", ""),
            "project_code": project_info.get("project_code", ""),
            "investment_entity": project_info.get("investment_entity", ""),
            "construction_content": project_info.get("construction_content", ""),
            "project_overview": project_info.get("project_overview", ""),
            "project_establishment_name": project_info.get("project_establishment_name", ""),

            # 进度和状态信息
            "current_progress": project_info.get("current_progress", ""),
            "next_steps": project_info.get("next_steps", ""),
            "issues_to_be_coordinated_resolved": project_info.get("issues_to_be_coordinated_resolved", ""),
            "is_investment_plan_done": project_info.get("is_investment_plan_done", ""),
            "is_project_established": project_info.get("is_project_established", ""),
            "excellence_level": project_info.get("excellence_level", ""),

            # 时间信息
            "start_time": str(project_info.get("start_time", "")),
            "acceptance_time": str(project_info.get("acceptance_time", "")),
            "project_establishment_time": str(project_info.get("project_establishment_time", "")),
            "project_implementation_time": str(project_info.get("project_implementation_time", "")),
            "project_procurement_time": str(project_info.get("project_procurement_time", "")),
            "project_acceptance_time": str(project_info.get("project_acceptance_time", "")),
            "business_research_time": str(project_info.get("business_research_time", "")),
            "solution_time": str(project_info.get("solution_time", "")),
            "project_establishment_year": project_info.get("project_establishment_year", ""),

            # 人员信息
            "responsible_person": project_info.get("responsible_person", ""),
            "responsible_department": project_info.get("responsible_department", ""),
            "b_person": project_info.get("b_person", ""),
            "developer_1": project_info.get("developer_1", ""),
            "developer_2": project_info.get("developer_2", ""),
            "itbp_team_member": project_info.get("itbp_team_member", ""),
            "responsible_person_B": project_info.get("responsible_person_B", ""),
            "b_person_B": project_info.get("b_person_B", ""),
            "developer_1_B": project_info.get("developer_1_B", ""),
            "developer_2_B": project_info.get("developer_2_B", ""),
            "itbp_team_member_B": project_info.get("itbp_team_member_B", ""),

            # 财务信息
            "annual_investment_plan": project_info.get("annual_investment_plan", ""),
            "budget": project_info.get("budget", ""),
            "project_planned_total_investment": project_info.get("project_planned_total_investment", ""),
            "software_hardware_product_procurement": project_info.get("software_hardware_product_procurement", ""),
            "technology_development": project_info.get("technology_development", ""),
            "system_integration": project_info.get("system_integration", ""),
            "others": project_info.get("others", ""),
            "estimated_operating_revenue": project_info.get("estimated_operating_revenue", ""),
            "estimated_project_cost": project_info.get("estimated_project_cost", ""),
            "estimated_gross_profit": project_info.get("estimated_gross_profit", ""),
            "gross_profit_margin": project_info.get("gross_profit_margin", ""),

            # 分类信息
            "project_category": project_info.get("project_category", ""),
            "investment_type": project_info.get("investment_type", ""),
            "is_hardware": project_info.get("is_hardware", ""),
            "is_non_indigenous_innovation": project_info.get("is_non_indigenous_innovation", ""),
            "line_work_special_project_work": project_info.get("line_work_special_project_work", ""),

            # 其他信息
            "remarks": project_info.get("remarks", ""),
            "business_number": project_info.get("business_number", ""),
            "progress_plan_locked": project_info.get("progress_plan_locked", "")
        }

        steps_info["step3_get_project_info"] = {
            "status": "completed",
            "message": f"项目记录获取完成，项目名称：{current_fields['project_name']}",
            "data": {
                "project_code": project_code,
                "current_fields": current_fields,
                "total_fields": len(current_fields),
                "non_empty_fields": len([v for v in current_fields.values() if v and str(v).strip()])
            }
        }

        # 步骤4：生成AI提示词
        logger.info(f"步骤4：生成AI提示词")
        steps_info["step4_generate_prompt"]["status"] = "processing"
        steps_info["step4_generate_prompt"]["message"] = "正在生成AI提示词..."

        # 根据档案阶段推断当前进度
        if archive_stages:
            # 按阶段顺序排序，取最新的阶段
            stage_order = ["立项阶段", "采购阶段", "实施阶段", "验收阶段", "结项阶段"]
            latest_stage = None
            for stage in stage_order:
                if stage in archive_stages:
                    latest_stage = stage
            if latest_stage:
                suggested_progress = latest_stage
            else:
                suggested_progress = archive_stages[-1] if archive_stages else None
        else:
            suggested_progress = None

        files_content = "\n\n".join([f"文件: {f['filename']}\n内容:\n{f['content']}" for f in markdown_files])

        ai_prompt = f"""你是一个专业的项目管理助手。请基于以下项目档案文件内容，分析并建议更新项目信息。

当前项目信息：
{json.dumps(current_fields, ensure_ascii=False, indent=2)}

项目档案阶段分析：
存在的档案阶段文件夹：{archive_stages}
根据档案阶段推断的当前进度：{suggested_progress}

项目档案文件内容：
{files_content}

**重要：请直接返回Markdown表格格式的更新建议，不要包含任何思考过程或解释文字。**

请仔细分析档案文件内容，对比当前项目信息，按照以下格式返回建议的更新字段（只返回需要更新的字段）：

| 字段名称 | 当前值 | 建议值 | 更新原因 |
|---------|--------|--------|----------|
| project_name | {current_fields.get('project_name', '')} | [如果档案中有更准确的名称] | [基于档案内容的原因] |
| project_establishment_name | {current_fields.get('project_establishment_name', '')} | [档案中的项目立项名称] | [基于档案内容的原因] |
| current_progress | {current_fields.get('current_progress', '')} | [根据档案阶段判断的进度] | [基于档案阶段的原因] |
| responsible_person | {current_fields.get('responsible_person', '')} | [档案中提到的责任人] | [基于档案内容的原因] |
| responsible_department | {current_fields.get('responsible_department', '')} | [档案中提到的负责部门] | [基于档案内容的原因] |
| construction_content | {current_fields.get('construction_content', '')} | [基于档案内容的建设内容] | [基于档案内容的原因] |
| project_overview | {current_fields.get('project_overview', '')} | [基于档案内容的项目概述] | [基于档案内容的原因] |
| start_time | {current_fields.get('start_time', '')} | [YYYY-MM-DD格式的开始时间] | [基于档案内容的原因] |
| acceptance_time | {current_fields.get('acceptance_time', '')} | [YYYY-MM-DD格式的验收时间] | [基于档案内容的原因] |
| project_establishment_time | {current_fields.get('project_establishment_time', '')} | [YYYY-MM-DD格式的立项时间] | [基于档案内容的原因] |
| project_implementation_time | {current_fields.get('project_implementation_time', '')} | [YYYY-MM-DD格式的实施时间] | [基于档案内容的原因] |
| annual_investment_plan | {current_fields.get('annual_investment_plan', '')} | [档案中提到的年度投资计划] | [基于档案内容的原因] |
| budget | {current_fields.get('budget', '')} | [档案中提到的预算金额] | [基于档案内容的原因] |
| project_planned_total_investment | {current_fields.get('project_planned_total_investment', '')} | [档案中提到的项目计划总投资] | [基于档案内容的原因] |
| investment_type | {current_fields.get('investment_type', '')} | [新建/续建/改造等] | [基于档案内容的原因] |
| is_hardware | {current_fields.get('is_hardware', '')} | [硬件/非硬件] | [基于档案内容的原因] |
| next_steps | {current_fields.get('next_steps', '')} | [档案中提到的下一步工作] | [基于档案内容的原因] |
| developer_1 | {current_fields.get('developer_1', '')} | [档案中提到的开发人员] | [基于档案内容的原因] |
| itbp_team_member | {current_fields.get('itbp_team_member', '')} | [档案中提到的ITBP团队成员] | [基于档案内容的原因] |
| remarks | {current_fields.get('remarks', '')} | [档案中的重要备注信息] | [基于档案内容的原因] |

分析要求：
1. 只包含确实需要更新的字段，如果档案中没有相关信息或与当前信息一致，则不要包含该行
2. 严禁编造或虚构任何信息，只能基于档案文件中明确提到的内容
3. 如果档案中某个字段标注为"（待确定）"、"（待填写）"或类似表述，不要建议具体值
4. 项目进度必须根据档案阶段文件夹来判断
5. 日期格式必须是YYYY-MM-DD，且必须是档案中明确提到的日期
6. 责任人必须是档案中明确提到的真实姓名
"""

        steps_info["step4_generate_prompt"] = {
            "status": "completed",
            "message": f"AI提示词生成完成，提示词长度：{len(ai_prompt):,} 字符",
            "data": {
                "prompt": ai_prompt,
                "prompt_length": len(ai_prompt),
                "archive_stages": archive_stages,
                "suggested_progress": suggested_progress,
                "files_included": len(markdown_files)
            }
        }

        # 步骤5：AI分析处理
        logger.info(f"步骤5：AI分析处理")
        steps_info["step5_ai_analysis"]["status"] = "processing"
        steps_info["step5_ai_analysis"]["message"] = "正在调用AI大模型分析..."

        # AI服务配置
        from app.services.ai_chat import AIChatService
        API_URL = os.getenv("DEEPSEEK_API_URL", "http://10.0.10.41:8000/v1/chat/completions")
        BACKUP_API_URLS = [
            "http://10.0.10.41:8000/v1/chat/completions",
            "http://10.0.10.42:8000/v1/chat/completions",
            "http://10.0.10.43:8000/v1/chat/completions"
        ]
        API_KEY = os.getenv("DEEPSEEK_API_KEY", "szjf@2025")
        API_MODEL = os.getenv("DEEPSEEK_MODEL", "/models/Qwen3-32B")

        ai_service = AIChatService(
            api_url=API_URL,
            api_key=API_KEY,
            model=API_MODEL,
            backup_urls=BACKUP_API_URLS
        )

        # 调用AI分析
        ai_response = await ai_service.generate_file_answer(ai_prompt)

        steps_info["step5_ai_analysis"] = {
            "status": "completed",
            "message": f"AI分析完成，回答长度：{len(ai_response):,} 字符",
            "data": {
                "ai_response": ai_response,
                "response_length": len(ai_response),
                "model_used": API_MODEL
            }
        }

        # 步骤6：准备用户确认数据
        logger.info(f"步骤6：准备用户确认数据")
        steps_info["step6_user_confirm"]["status"] = "ready"
        steps_info["step6_user_confirm"]["message"] = "AI分析完成，等待用户确认更新"
        steps_info["step6_user_confirm"]["data"] = {
            "ai_response_markdown": ai_response,
            "ready_for_confirmation": True
        }

        return {
            "code": 200,
            "message": "AI更新分析完成，请确认更新内容",
            "data": {
                "project_code": project_code,
                "steps": steps_info,
                "summary": {
                    "total_steps": 6,
                    "completed_steps": 5,
                    "files_analyzed": len(markdown_files),
                    "content_length": total_content_length,
                    "ai_response_preview": ai_response[:500] + "..." if len(ai_response) > 500 else ai_response
                }
            }
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"AI更新项目异常: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/{project_code}/ai-update/confirm")
async def confirm_ai_update(
    project_code: str = Path(..., description="项目编号"),
    update_data: Dict = Body(..., description="确认的更新数据")
) -> Dict[str, Any]:
    """
    确认并应用AI建议的项目更新
    """
    try:
        logger.info(f"确认AI更新项目: {project_code}")

        # 验证更新数据
        if not update_data or "confirmed_updates" not in update_data:
            raise HTTPException(status_code=400, detail="缺少确认的更新数据")

        confirmed_updates = update_data["confirmed_updates"]
        if not confirmed_updates:
            return {
                "code": 400,
                "message": "没有需要更新的字段",
                "data": None
            }

        # 添加项目编号
        confirmed_updates["project_code"] = project_code

        # 调用现有的项目更新API
        event = {
            "body": json.dumps(confirmed_updates)
        }

        result = update_project_details_handler(event, None)

        if result.get("statusCode") == 200:
            body = json.loads(result.get("body", "{}"))
            return {
                "code": 200,
                "message": "AI更新应用成功",
                "data": {
                    "project_code": project_code,
                    "updated_fields": list(confirmed_updates.keys()),
                    "update_result": body.get("data", {})
                }
            }
        else:
            logger.error(f"应用AI更新失败: {result}")
            raise HTTPException(status_code=result.get("statusCode", 500), detail="应用更新失败")

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"确认AI更新异常: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

# 规划类别相关的数据模型
class UpdateCategoryRequest(BaseModel):
    project_codes: List[str]
    category_level2: str

@router.get("/categories/options")
async def get_category_options(
    current_user: Dict = Depends(get_current_user)
) -> Dict[str, Any]:
    """
    获取规划类别选项
    """
    try:
        conn = get_db_connection()
        with conn.cursor() as cursor:
            # 获取完整的分层结构数据
            cursor.execute("""
                SELECT DISTINCT category_level1
                FROM project_plan
                WHERE category_level1 IS NOT NULL AND category_level1 != ''
                ORDER BY category_level1
            """)
            level1_categories = cursor.fetchall()

            # 构建分层数据结构
            hierarchy_data = {}
            category_list = []  # 保持向后兼容

            for level1_cat in level1_categories:
                level1_name = level1_cat['category_level1']

                # 获取该一级分类下的所有二级分类
                cursor.execute("""
                    SELECT DISTINCT category_level2
                    FROM project_plan
                    WHERE category_level1 = %s
                    AND category_level2 IS NOT NULL AND category_level2 != ''
                    ORDER BY category_level2
                """, (level1_name,))

                level2_categories = cursor.fetchall()
                level2_list = [cat['category_level2'] for cat in level2_categories]

                hierarchy_data[level1_name] = level2_list
                category_list.extend(level2_list)  # 保持向后兼容

            logger.info(f"从project_plan表获取到的分层结构: {len(level1_categories)}个一级分类, {len(category_list)}个二级分类")

        conn.close()

        return {
            "code": 200,
            "message": "获取规划类别选项成功",
            "data": category_list,  # 保持向后兼容
            "hierarchy": hierarchy_data  # 新增分层数据
        }

    except Exception as e:
        logger.error(f"获取规划类别选项失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/update-category")
async def update_project_category(
    request: UpdateCategoryRequest,
    current_user: Dict = Depends(get_current_user)
) -> Dict[str, Any]:
    """
    批量更新项目的规划类别
    """
    try:
        if not request.project_codes:
            raise HTTPException(status_code=400, detail="项目编号列表不能为空")

        conn = get_db_connection()
        updated_count = 0

        with conn.cursor() as cursor:
            # 批量更新项目的规划类别
            for project_code in request.project_codes:
                cursor.execute("""
                    UPDATE project_account_book
                    SET category_level2 = %s
                    WHERE project_code = %s
                """, (request.category_level2, project_code))

                if cursor.rowcount > 0:
                    updated_count += 1

        conn.commit()
        conn.close()

        return {
            "code": 200,
            "message": f"成功更新 {updated_count} 个项目的规划类别",
            "data": {
                "updated_count": updated_count,
                "total_count": len(request.project_codes),
                "category_level2": request.category_level2
            }
        }

    except Exception as e:
        logger.error(f"更新项目规划类别失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/export/excel")
async def export_projects_excel(
    investment_entity: Optional[str] = Query(None, description="投资主体筛选"),
    project_name: Optional[str] = Query(None, description="项目名称搜索"),
    current_progress: Optional[str] = Query(None, description="当前进度筛选"),
    category_level2: Optional[str] = Query(None, description="规划类别筛选")
):
    """导出项目数据为Excel"""
    try:
        conn = get_db_connection()
        with conn.cursor() as cursor:
            # 构建查询条件
            where_conditions = []
            params = []

            if investment_entity:
                where_conditions.append("investment_entity = %s")
                params.append(investment_entity)

            if project_name:
                where_conditions.append("project_name LIKE %s")
                params.append(f"%{project_name}%")

            if current_progress:
                where_conditions.append("current_progress = %s")
                params.append(current_progress)

            if category_level2:
                where_conditions.append("category_level2 = %s")
                params.append(category_level2)

            where_clause = " WHERE " + " AND ".join(where_conditions) if where_conditions else ""

            # 获取所有数据
            sql = f"SELECT * FROM project_account_book{where_clause} ORDER BY project_code ASC"
            cursor.execute(sql, params)
            projects = cursor.fetchall()

        conn.close()

        if not projects:
            raise HTTPException(status_code=404, detail="没有找到符合条件的项目数据")

        # 转换数据格式 - 将字典列表转换为DataFrame可以处理的格式
        project_data = []
        for project in projects:
            # 将每个项目字典转换为普通字典（处理可能的特殊对象）
            project_dict = {}
            for key, value in project.items():
                # 处理特殊值
                if value is None:
                    project_dict[key] = ""
                elif isinstance(value, (int, float, str)):
                    project_dict[key] = value
                else:
                    project_dict[key] = str(value)
            project_data.append(project_dict)

        # 创建DataFrame
        df = pd.DataFrame(project_data)

        # 不使用中文列名，直接使用英文列名避免编码问题
        # 创建简化的列名映射
        columns_to_keep = [
            'id', 'investment_entity', 'project_name', 'project_code',
            'annual_investment_plan', 'current_progress', 'responsible_person',
            'project_category', 'budget', 'start_time', 'acceptance_time'
        ]

        # 只保留主要列
        if len(df.columns) > len(columns_to_keep):
            # 如果有更多列，保留所有列但不重命名
            pass
        else:
            df = df[columns_to_keep] if all(col in df.columns for col in columns_to_keep) else df

        # 创建Excel文件
        output = io.BytesIO()
        with pd.ExcelWriter(output, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name='项目数据', index=False)

        output.seek(0)

        # 生成文件名
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"projects_export_{timestamp}.xlsx"

        return StreamingResponse(
            io.BytesIO(output.read()),
            media_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            headers={"Content-Disposition": f"attachment; filename={filename}"}
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"导出Excel失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"导出Excel失败: {str(e)}")

@router.get("/options")
async def get_project_options():
    """获取项目筛选选项"""
    try:
        conn = get_db_connection()
        with conn.cursor() as cursor:
            # 获取投资主体选项
            cursor.execute("SELECT DISTINCT investment_entity FROM project_account_book WHERE investment_entity IS NOT NULL AND investment_entity != ''")
            investment_entities = [row['investment_entity'] for row in cursor.fetchall()]

            # 获取当前进度选项
            cursor.execute("SELECT DISTINCT current_progress FROM project_account_book WHERE current_progress IS NOT NULL AND current_progress != ''")
            progress_options = [row['current_progress'] for row in cursor.fetchall()]

            # 获取规划类别选项
            cursor.execute("SELECT DISTINCT category_level2 FROM project_account_book WHERE category_level2 IS NOT NULL AND category_level2 != ''")
            category_options = [row['category_level2'] for row in cursor.fetchall()]

            # 获取项目类别选项
            cursor.execute("SELECT DISTINCT project_category FROM project_account_book WHERE project_category IS NOT NULL AND project_category != ''")
            project_category_options = [row['project_category'] for row in cursor.fetchall()]

            # 获取负责部门选项
            cursor.execute("SELECT DISTINCT responsible_department FROM project_account_book WHERE responsible_department IS NOT NULL AND responsible_department != ''")
            department_options = [row['responsible_department'] for row in cursor.fetchall()]

        conn.close()

        return {
            "code": 200,
            "success": True,
            "message": "获取筛选选项成功",
            "data": {
                "investment_entities": sorted(investment_entities),
                "progress_options": sorted(progress_options),
                "category_options": sorted(category_options),
                "project_category_options": sorted(project_category_options),
                "department_options": sorted(department_options)
            }
        }

    except Exception as e:
        logger.error(f"获取筛选选项失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取筛选选项失败: {str(e)}")

@router.get("/template/excel")
async def download_excel_template():
    """下载Excel导入模板"""
    try:
        # 创建简化的英文模板，避免编码问题
        template_data = {
            'investment_entity': ['Example: Finance Insurance', ''],
            'project_name': ['Example: Core System Upgrade', ''],
            'project_code': ['Example: C202500001', ''],
            'annual_investment_plan': ['Example: 500', ''],
            'current_progress': ['Example: Project Initiation', ''],
            'responsible_person': ['Example: Zhang San', ''],
            'project_category': ['Example: IT Construction', ''],
            'budget': ['Example: 1000', ''],
            'start_time': ['Example: 2025-01-01', ''],
            'acceptance_time': ['Example: 2025-12-31', '']
        }

        df = pd.DataFrame(template_data)

        # 创建Excel文件
        output = io.BytesIO()
        with pd.ExcelWriter(output, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name='Template', index=False)

            # 获取工作表并设置样式
            worksheet = writer.sheets['Template']

            # 设置列宽
            for column in worksheet.columns:
                max_length = 0
                column_letter = column[0].column_letter
                for cell in column:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass
                adjusted_width = min(max_length + 2, 50)
                worksheet.column_dimensions[column_letter].width = adjusted_width

        output.seek(0)

        return StreamingResponse(
            io.BytesIO(output.read()),
            media_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            headers={"Content-Disposition": "attachment; filename=template.xlsx"}
        )

    except Exception as e:
        logger.error(f"下载模板失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"下载模板失败: {str(e)}")
@router.delete("/batch")
async def batch_delete_projects(project_ids: list[int]):
    """批量删除项目"""
    try:
        if not project_ids:
            raise HTTPException(status_code=400, detail="项目ID列表不能为空")
        
        conn = get_db_connection()
        with conn.cursor() as cursor:
            # 构建删除SQL
            placeholders = ','.join(['%s'] * len(project_ids))
            sql = f"DELETE FROM project_account_book WHERE id IN ({placeholders})"
            cursor.execute(sql, project_ids)
            deleted_count = cursor.rowcount
        
        conn.commit()
        conn.close()
        
        return {
            "code": 200,
            "success": True,
            "message": f"成功删除 {deleted_count} 个项目",
            "data": {"deleted_count": deleted_count}
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"批量删除项目失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"批量删除项目失败: {str(e)}")

@router.delete("/{project_id}")
async def delete_project(project_id: int):
    """删除单个项目"""
    try:
        conn = get_db_connection()
        with conn.cursor() as cursor:
            # 检查项目是否存在
            cursor.execute("SELECT id FROM project_account_book WHERE id = %s", (project_id,))
            if not cursor.fetchone():
                raise HTTPException(status_code=404, detail="项目不存在")

            # 删除项目
            cursor.execute("DELETE FROM project_account_book WHERE id = %s", (project_id,))
            deleted_count = cursor.rowcount

        conn.commit()
        conn.close()

        if deleted_count == 0:
            raise HTTPException(status_code=404, detail="项目不存在")

        return {
            "code": 200,
            "success": True,
            "message": "项目删除成功",
            "data": {"deleted_count": deleted_count}
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除项目失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"删除项目失败: {str(e)}")
