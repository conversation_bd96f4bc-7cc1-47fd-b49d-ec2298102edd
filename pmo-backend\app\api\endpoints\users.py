from fastapi import APIRouter, Depends, HTTPException, Query
from typing import Dict, Any, Optional, List
import json

from app.core.security import get_current_user
from app.core.logger import get_logger

# 导入云函数
from app.cloud_functions.getuser.index import main_handler as get_user_handler
from app.cloud_functions.user_project_management_py.index import main_handler as user_project_management_handler

logger = get_logger(__name__)
router = APIRouter()

@router.get("/")
async def get_users(
    current_user: Dict = Depends(get_current_user)
) -> Dict[str, Any]:
    """
    获取用户列表
    """
    try:
        import os

        # 测试模式：返回测试用户列表
        if os.getenv("PMO_TEST_MODE") == "true":
            logger.info("测试模式：返回测试用户列表")
            return {
                "code": 200,
                "message": "获取用户列表成功",
                "data": [
                    {
                        "UserID": "na10000014",
                        "username": "TestUser",
                        "name": "测试用户",
                        "role": 3.0,
                        "department_name": "业务二部",
                        "company_name": "数字金服",
                        "LaborCost": 20.0
                    }
                ]
            }

        # 构造云函数需要的event对象
        event = {}

        # 调用云函数
        result = get_user_handler(event, None)

        # 解析云函数响应
        if result.get("statusCode") == 200:
            body = json.loads(result.get("body", "{}"))
            return body
        else:
            logger.error(f"获取用户列表失败: {result}")
            raise HTTPException(status_code=result.get("statusCode", 500), detail="获取用户列表失败")
    except Exception as e:
        logger.error(f"获取用户列表异常: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/projects")
async def get_user_projects(
    username: Optional[str] = Query(None, description="用户名"),
    current_user: Dict = Depends(get_current_user)
) -> Dict[str, Any]:
    """
    获取用户项目
    """
    try:
        # 构造云函数需要的event对象
        event = {
            "queryStringParameters": {
                "action": "get",
                "username": username or current_user.get("username")
            }
        }
        
        # 调用云函数
        result = user_project_management_handler(event, None)
        
        # 解析云函数响应
        if result.get("statusCode") == 200:
            body = json.loads(result.get("body", "{}"))
            return body
        else:
            logger.error(f"获取用户项目失败: {result}")
            raise HTTPException(status_code=result.get("statusCode", 500), detail="获取用户项目失败")
    except Exception as e:
        logger.error(f"获取用户项目异常: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/projects")
async def update_user_projects(
    project_data: Dict,
    current_user: Dict = Depends(get_current_user)
) -> Dict[str, Any]:
    """
    更新用户项目
    """
    try:
        # 构造云函数需要的event对象
        event = {
            "queryStringParameters": {
                "action": "update"
            },
            "body": json.dumps(project_data)
        }
        
        # 调用云函数
        result = user_project_management_handler(event, None)
        
        # 解析云函数响应
        if result.get("statusCode") == 200:
            body = json.loads(result.get("body", "{}"))
            return body
        else:
            logger.error(f"更新用户项目失败: {result}")
            raise HTTPException(status_code=result.get("statusCode", 500), detail="更新用户项目失败")
    except Exception as e:
        logger.error(f"更新用户项目异常: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e)) 